# =============================================================================
# API Keys - Multi-Provider Support (Required to enable respective provider)
# =============================================================================

# Text-to-Text Providers
XAI_API_KEY="your_xai_api_key_here"
OPENAI_API_KEY="your_openai_api_key_here"
COHERE_API_KEY="your_cohere_api_key_here"
ANTHROPIC_API_KEY="your_anthropic_api_key_here"
OPENROUTER_API_KEY="your_openrouter_api_key_here"
HUGGINGFACE_API_KEY="your_huggingface_api_key_here"
OLLAMA_API_KEY=""  # Optional for Ollama

# Additional API Keys (for other integrations)
PERPLEXITY_API_KEY="your_perplexity_api_key_here"
GOOGLE_API_KEY="your_google_api_key_here"
MISTRAL_API_KEY="your_mistral_key_here"
AZURE_OPENAI_API_KEY="your_azure_key_here"
GITHUB_API_KEY="your_github_api_key_here"

# =============================================================================
# Provider Selection Configuration
# =============================================================================

# Manual Provider Selection (optional - overrides auto-detection)
MANUAL_TEXT_PROVIDER=xai
MANUAL_EMBEDDING_PROVIDER=openai

# Auto-detected providers will be used if manual selection not set
# Priority order: xai, openai, cohere, anthropic, openrouter, huggingface, ollama

# =============================================================================
# Ollama Configuration (Endpoint Required, API Key Optional)
# =============================================================================
OLLAMA_ENDPOINT="http://localhost:11434"  # Required
OLLAMA_HOST="localhost:11434"  # Alternative to OLLAMA_ENDPOINT
OLLAMA_TEXT_MODEL="llama3.1"
OLLAMA_EMBEDDING_MODEL="nomic-embed-text"

# For Docker environments (uncomment if using Docker)
# OLLAMA_ENDPOINT="http://ollama:11434"

# =============================================================================
# Cognee Configuration (Updated for Multi-Provider)
# =============================================================================

# LLM Configuration (will use selected text provider)
LLM_API_KEY=${XAI_API_KEY}  # Default to XAI key
LLM_PROVIDER="openai"  # xAI uses OpenAI-compatible API
LLM_MODEL="grok-beta"  # or "grok-4" based on your preference

# =============================================================================
# Email Service API Keys
# =============================================================================
RESEND_API_KEY="your_resend_api_key_here"

LLM_ENDPOINT="https://api.x.ai/v1"

# Embedding Configuration (separate from LLM - xAI doesn't have embeddings)
EMBEDDING_PROVIDER="openai"  # Required: xAI doesn't provide embeddings
EMBEDDING_MODEL="text-embedding-3-large"
EMBEDDING_API_KEY="your_openai_api_key_here"  # Required for embeddings

# Processing Configuration
CHUNK_SIZE=1024
CHUNK_OVERLAP=128
USE_COGNEE=true
ENABLE_AI=true

# =============================================================================
# Graph Database Configuration (Neo4j)
# =============================================================================
GRAPH_DATABASE_PROVIDER=neo4j
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD="your_neo4j_password_here"

# Local development override (uncomment for local development)
# NEO4J_URI=bolt://localhost:7687

# =============================================================================
# Vector Database Configuration (Qdrant)
# =============================================================================
VECTOR_DB_PROVIDER=qdrant
VECTOR_DB_URL=http://localhost:6333
VECTOR_DB_KEY=""  # Empty for local development

# Local development override (uncomment for local development)
# VECTOR_DB_URL=http://localhost:6333

# =============================================================================
# Relational Database Configuration (PostgreSQL)
# =============================================================================
DB_PROVIDER=postgres
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME="your_postgres_username_here"
DB_PASSWORD="your_postgres_password_here"
DB_NAME="your_postgres_db_name_here"
DB_SSL_MODE=disable

# Local development override (uncomment for local development)
# DB_HOST=localhost
# DB_USERNAME=cognee
# DB_PASSWORD=cognee
# DB_NAME=cognee_db

# =============================================================================
# Redis Configuration
# =============================================================================
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=""

# Local development override (uncomment for local development)
# REDIS_URL=redis://localhost:6379/0

# =============================================================================
# FastAPI Application Configuration
# =============================================================================
DEBUG=true
LOG_LEVEL=info
ENVIRONMENT=development
API_HOST=0.0.0.0
API_PORT=8000

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000", "http://localhost:5173"]

# =============================================================================
# File Processing Configuration
# =============================================================================
MAX_FILE_SIZE=104857600  # 100MB in bytes
UPLOAD_PATH=/app/uploads
PROCESSED_PATH=/app/processed
MAX_CONCURRENT_UPLOADS=10
SUPPORTED_FILE_TYPES=["json", "csv", "txt", "md", "pdf", "png", "jpg", "jpeg"]

# =============================================================================
# Celery Configuration
# =============================================================================
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=["json"]
CELERY_TIMEZONE=UTC
CELERY_ENABLE_UTC=true

# =============================================================================
# Security Configuration
# =============================================================================
SECRET_KEY="your_secret_key_here"
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# =============================================================================
# Monitoring and Logging
# =============================================================================
ENABLE_METRICS=true
METRICS_PORT=9090
LOG_FORMAT=json
LOG_FILE=/app/logs/app.log

# =============================================================================
# Provider Model Configurations (Optional Overrides)
# =============================================================================

# Text-to-Text Model Overrides
XAI_MODEL="grok-beta"
OPENAI_MODEL="gpt-4-turbo-preview"
COHERE_MODEL="command-r-plus"
ANTHROPIC_MODEL="claude-3-5-sonnet-20241022"
OPENROUTER_MODEL="meta-llama/llama-3.1-8b-instruct:free"
HUGGINGFACE_MODEL="microsoft/DialoGPT-medium"
OLLAMA_MODEL="llama3.1"

# Embedding Model Overrides
OPENAI_EMBEDDING_MODEL="text-embedding-3-large"
OPENROUTER_EMBEDDING_MODEL="text-embedding-3-large"
HUGGINGFACE_EMBEDDING_MODEL="sentence-transformers/all-MiniLM-L6-v2"
OLLAMA_EMBEDDING_MODEL="nomic-embed-text"

# =============================================================================
# Provider Endpoint Overrides (Optional)
# =============================================================================
XAI_ENDPOINT="https://api.x.ai/v1"
OPENAI_ENDPOINT="https://api.openai.com/v1"
COHERE_ENDPOINT="https://api.cohere.ai/v1"
ANTHROPIC_ENDPOINT="https://api.anthropic.com/v1"
OPENROUTER_ENDPOINT="https://openrouter.ai/api/v1"
HUGGINGFACE_ENDPOINT="https://api-inference.huggingface.co/models"

# =============================================================================
# Production Overrides (uncomment for production)
# =============================================================================
# DEBUG=false
# ENVIRONMENT=production
# LOG_LEVEL=warning
# NEO4J_URI=neo4j+s://your-production-instance.databases.neo4j.io:7687
# VECTOR_DB_URL=https://your-cluster.cloud.qdrant.io:6333
# VECTOR_DB_KEY=your-production-qdrant-key
# DB_HOST=your-production-db-host
# DB_SSL_MODE=require

# =============================================================================
# Development Features (uncomment for specific testing)
# =============================================================================
# ENABLE_DEBUG_LOGGING=true
# DISABLE_AUTHENTICATION=true
# MOCK_EXTERNAL_APIS=false
# ENABLE_PROFILING=false

# ====
# Resend for sending OTP
# ====
RESEND_API_KEY="your_resend_api_key_here"

# ====
# NextAuth and Application Settings
# ====
NEXTAUTH_URL=http://localhost:4002
DATABASE_URL="postgresql://your_username:your_password@localhost:5433/your_db_name?schema=public"

# You can use openssl to generate a random 32 character key: openssl rand -base64 32
NEXTAUTH_SECRET="your_nextauth_secret_here"

# SMTP / Email settings
SMTP_HOST="your_smtp_host_here"
SMTP_PORT="your_smtp_port_here"
SMTP_USER="your_smtp_user_here"
SMTP_PASSWORD="your_smtp_password_here"
SMTP_FROM="your_smtp_from_here"

# If you are using Docker, you can retrieve the values from: docker-compose.yml

APP_URL=http://localhost:4002

SVIX_URL="your_svix_url_here"
SVIX_API_KEY="your_svix_api_key_here"

GITHUB_CLIENT_ID="your_github_client_id_here"
GITHUB_CLIENT_SECRET="your_github_client_secret_here"

GOOGLE_CLIENT_ID="your_google_client_id_here"
GOOGLE_CLIENT_SECRET="your_google_client_secret_here"

RETRACED_URL="your_retraced_url_here"
RETRACED_API_KEY="your_retraced_api_key_here"
RETRACED_PROJECT_ID="your_retraced_project_id_here"

# Hide landing page and redirect to login page
HIDE_LANDING_PAGE=false

# SSO groups can be prefixed with this identifier to avoid conflicts with other groups.
# For example boxyhq-admin would be resolved to admin, boxyhq-member would be resolved to member, etc.
GROUP_PREFIX=boxyhq-

# Users will need to confirm their email before accessing the app feature
CONFIRM_EMAIL=false

# Disable non-business email signup
DISABLE_NON_BUSINESS_EMAIL_SIGNUP=false

# Mixpanel
NEXT_PUBLIC_MIXPANEL_TOKEN="your_mixpanel_token_here"

# If you want to use Jackson that is self-hosted or our SaaS instead of the embedded Jackson that comes with SaaS Starter Kit
# JACKSON_URL=http://localhost:5225
# JACKSON_EXTERNAL_URL=https://sso.eu.boxyhq.com
# JACKSON_API_KEY=secret
# JACKSON_PRODUCT_ID=boxyhq
# JACKSON_WEBHOOK_SECRET=your-webhook-secret

# Enable Auth providers (comma separated)
# Supported providers: github, google, saml, email, credentials, idp-initiated
AUTH_PROVIDERS=

# OpenTelemetry
OTEL_EXPORTER_OTLP_METRICS_ENDPOINT="your_otel_metrics_endpoint_here"
OTEL_EXPORTER_OTLP_METRICS_HEADERS="your_otel_metrics_headers_here"
# OTEL_EXPORTER_OTLP_METRICS_PROTOCOL=grpc
# If you have any issues with using the otel exporter and want to enable debug logs uncomment below
# OTEL_EXPORTER_DEBUG=true
OTEL_PREFIX=boxyhq.saas

NEXT_PUBLIC_TERMS_URL='/terms'
NEXT_PUBLIC_PRIVACY_URL='/privacy'

NEXT_PUBLIC_DARK_MODE=false

# Team feature
FEATURE_TEAM_SSO=true
FEATURE_TEAM_DSYNC=true
FEATURE_TEAM_AUDIT_LOG=true
FEATURE_TEAM_WEBHOOK=true
FEATURE_TEAM_API_KEY=true
FEATURE_TEAM_DELETION=true
FEATURE_TEAM_PAYMENTS=true

# Google reCAPTCHA
RECAPTCHA_SITE_KEY="your_recaptcha_site_key_here"
RECAPTCHA_SECRET_KEY="your_recaptcha_secret_key_here"

# Decide which session strategy (jwt or database) to use with NextAuth
NEXTAUTH_SESSION_STRATEGY=jwt

# Sentry
NEXT_PUBLIC_SENTRY_DSN="your_sentry_dsn_here"
NEXT_PUBLIC_SENTRY_TRACE_SAMPLE_RATE=0.0 # https://develop.sentry.dev/sdk/performance/#tracessamplerate
SENTRY_RELEASE="your_sentry_release_here"
SENTRY_ENVIRONMENT="your_sentry_environment_here"

# Sentry CLI (Set these values if you want to upload source maps to Sentry)
SENTRY_URL="your_sentry_url_here"
SENTRY_ORG="your_sentry_org_here"
SENTRY_PROJECT="your_sentry_project_here"
SENTRY_AUTH_TOKEN="your_sentry_auth_token_here"

# Max login attempts before account is locked
MAX_LOGIN_ATTEMPTS=5

# Set this to receive Slack notifications, https://hooks.slack.com/services/xxx/xxx/xxx
SLACK_WEBHOOK_URL="your_slack_webhook_url_here"

# Stripe
STRIPE_SECRET_KEY="your_stripe_secret_key_here"
STRIPE_WEBHOOK_SECRET="your_stripe_webhook_secret_here"

# Support URL
NEXT_PUBLIC_SUPPORT_URL="your_support_url_here"