FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libpoppler-cpp-dev \
    pkg-config \
    tesseract-ocr \
    libmagic1 \
    libreoffice \
    && rm -rf /var/lib/apt/lists/*

# Copy and install Python dependencies
COPY requirements.txt .
COPY app/processors/requirements.txt ./processor_requirements.txt

RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir -r processor_requirements.txt

# Install additional dependencies for integration
RUN pip install --no-cache-dir \
    "langchain-xai>=0.0.2" \
    "langchain-anthropic>=0.1.18" \
    "langchain-cohere>=0.1.10" \
    "langchain-openai>=0.1.26" \
    "langchain-google-genai>=1.0.10" \
    "langchain-groq>=0.1.10" \
    "langchain-mistralai>=0.1.12" \
    "langchain-community>=0.2.10" \
    "langchain-text-splitters>=0.2.5" \
    "langchain-experimental>=0.0.70" \
    "langchain-huggingface>=0.0.3" \
    "langchain-chroma>=0.1.2" \
    "langchain-pinecone>=0.1.2" \
    "langchain-qdrant>=0.1.3" \
    "langchain-voyageai>=0.1.4" \
    "langchain-together>=0.1.6" \
    "langchain-aws>=0.1.11" \
    "langchain-upstage>=0.1.7" \
    "langchain-ollama>=0.1.4"

# Copy the application code into the container
COPY ./app /app

# Create necessary directories
RUN mkdir -p /app/uploads /app/processed /app/logs

# Expose the port the app runs on
EXPOSE 8000

# Command to run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]