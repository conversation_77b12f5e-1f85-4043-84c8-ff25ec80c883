"""
Comprehensive Authentication API endpoints for ConTXT.
Provides user registration, login, email verification, password management, and API key management.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Request, BackgroundTasks
from fastapi.security import HTTPBearer
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from typing import List, Dict, Any
from datetime import datetime
import asyncpg
import logging

from app.core.auth_service import AuthService
from app.core.otp_service import OTPService
from app.core.email_service import EmailService
from app.core.auth_dependencies import (
    get_current_user, get_verified_user, get_premium_user, 
    get_client_ip, get_user_agent, get_db_pool
)
from app.schemas.auth import (
    UserRegistration, UserLogin, TokenResponse, RefreshTokenRequest,
    UserProfile, UserProfileUpdate, PasswordChange, PasswordResetOTP,
    OTPRequest, OTPVerification, ApiKeyCreate,
    ApiKeyResponse, ApiKeyList, AuthResponse, ErrorResponse
)
from app.config.settings import settings

logger = logging.getLogger(__name__)
router = APIRouter(tags=["Authentication"])
security = HTTPBearer()

# Rate limiter setup
limiter = Limiter(key_func=get_remote_address)
app = None  # Will be set when router is included

# Dependency to get services
def get_email_service() -> EmailService:
    return EmailService()

def get_otp_service(db_pool: asyncpg.Pool = Depends(get_db_pool), email_service: EmailService = Depends(get_email_service)) -> OTPService:
    return OTPService(db_pool, email_service)

def get_auth_service(db_pool: asyncpg.Pool = Depends(get_db_pool), otp_service: OTPService = Depends(get_otp_service), email_service: EmailService = Depends(get_email_service)) -> AuthService:
    """Get authentication service instance with all dependencies."""
    return AuthService(db_pool, otp_service, email_service)

# ============================================================================
# REGISTRATION & LOGIN ENDPOINTS
# ============================================================================

@router.post("/register", response_model=AuthResponse, status_code=status.HTTP_201_CREATED)
@limiter.limit(settings.REGISTER_RATE_LIMIT if settings.RATE_LIMIT_ENABLED else "1000/minute")
async def register(
    request: Request,
    user_data: UserRegistration,
    auth_service: AuthService = Depends(get_auth_service)
) -> AuthResponse:
    """
    Register new user with email verification.
    
    - **email**: Valid email address
    - **password**: Strong password (min 8 chars, uppercase, lowercase, digit)
    - **first_name**: Optional first name
    - **last_name**: Optional last name
    
    Returns registration confirmation with verification instructions.
    """
    try:
        result = await auth_service.register_user(user_data)
        return AuthResponse(
            message=result["message"],
            success=True,
            data={"user_id": result["user_id"]}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )

@router.post("/login", response_model=TokenResponse)
@limiter.limit(settings.LOGIN_RATE_LIMIT if settings.RATE_LIMIT_ENABLED else "1000/minute")
async def login(
    request: Request,
    login_data: UserLogin,
    auth_service: AuthService = Depends(get_auth_service)
) -> TokenResponse:
    """
    User login with JWT token generation.
    
    - **email**: User email address
    - **password**: User password
    
    Returns JWT access and refresh tokens.
    """
    try:
        ip_address = get_client_ip(request)
        user_agent = get_user_agent(request)
        
        return await auth_service.login_user(login_data, ip_address, user_agent)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )

@router.post("/refresh-token", response_model=TokenResponse)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    auth_service: AuthService = Depends(get_auth_service)
) -> TokenResponse:
    """
    Refresh JWT access token using refresh token.
    
    - **refresh_token**: Valid refresh token
    
    Returns new access token.
    """
    try:
        return await auth_service.refresh_access_token(refresh_data.refresh_token)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )

@router.post("/logout", response_model=AuthResponse)
async def logout(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service)
) -> AuthResponse:
    """
    Logout user and invalidate sessions.
    
    Invalidates all user sessions for security.
    """
    try:
        result = await auth_service.logout_user(current_user["id"])
        return AuthResponse(message=result["message"])
    except Exception as e:
        logger.error(f"Logout error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )

# ============================================================================
# EMAIL VERIFICATION ENDPOINTS
# ============================================================================

@router.post("/verify-email-otp", response_model=AuthResponse)
@limiter.limit(settings.VERIFY_EMAIL_RATE_LIMIT if settings.RATE_LIMIT_ENABLED else "1000/minute")
async def verify_email_otp(
    request: Request,
    verification_data: OTPVerification,
    auth_service: AuthService = Depends(get_auth_service)
) -> AuthResponse:
    """
    Verify user email with OTP.
    
    - **email**: User's email address
    - **otp**: 6-digit code from email
    
    Activates user account after successful verification.
    """
    try:
        result = await auth_service.verify_otp(verification_data)
        return AuthResponse(message=result["message"], success=True)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Email verification OTP error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Email verification failed"
        )

@router.post("/resend-verification-otp", response_model=AuthResponse)
@limiter.limit("3/hour")
async def resend_verification_otp(
    request: Request,
    otp_request_data: OTPRequest,
    auth_service: AuthService = Depends(get_auth_service)
) -> AuthResponse:
    """
    Resend email verification OTP.
    
    - **email**: User email address
    
    Sends a new verification OTP if the user exists and is unverified.
    """
    try:
        result = await auth_service.resend_verification_otp(otp_request_data)
        return AuthResponse(message=result["message"], success=True)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Resend verification OTP error for {otp_request_data.email}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while processing your request."
        )

# ============================================================================
# PASSWORD MANAGEMENT ENDPOINTS
# ============================================================================

@router.post("/request-password-reset-otp", response_model=AuthResponse)
@limiter.limit(settings.PASSWORD_RESET_RATE_LIMIT if settings.RATE_LIMIT_ENABLED else "1000/hour")
async def request_password_reset_otp(
    request: Request,
    otp_request_data: OTPRequest,
    auth_service: AuthService = Depends(get_auth_service)
) -> AuthResponse:
    """
    Request a password reset OTP.

    - **email**: The user's email address.

    Sends a password reset OTP to the user's email if the account exists.
    """
    try:
        result = await auth_service.request_password_reset_otp(otp_request_data)
        return AuthResponse(message=result["message"], success=True)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password reset request error for email {otp_request_data.email}: {e}")
        # Generic response to prevent email enumeration
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while processing your request."
        )

@router.post("/reset-password-with-otp", response_model=AuthResponse)
async def reset_password_with_otp(
    request: Request,
    reset_data: PasswordResetOTP,
    auth_service: AuthService = Depends(get_auth_service)
) -> AuthResponse:
    """
    Reset password with OTP.
    
    - **otp**: OTP code from email
    - **token**: Password reset token from email
    - **new_password**: New strong password
    
    Resets password and invalidates all user sessions.
    """
    try:
        result = await auth_service.reset_password_with_otp(reset_data)
        return AuthResponse(message=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password reset error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset failed"
        )

@router.post("/change-password", response_model=AuthResponse)
async def change_password(
    password_data: PasswordChange,
    current_user: Dict[str, Any] = Depends(get_verified_user),
    auth_service: AuthService = Depends(get_auth_service)
) -> AuthResponse:
    """
    Change user password (requires authentication).
    
    - **current_password**: Current password
    - **new_password**: New strong password
    
    Requires valid authentication and current password verification.
    """
    try:
        result = await auth_service.change_password(current_user["id"], password_data)
        return AuthResponse(message=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password change error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password change failed"
        )

# ============================================================================
# USER PROFILE ENDPOINTS
# ============================================================================

@router.get("/me", response_model=UserProfile)
async def get_profile(
    current_user: Dict[str, Any] = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service)
) -> UserProfile:
    """
    Get current user profile.
    
    Returns detailed user information for authenticated user.
    """
    try:
        return await auth_service.get_user_profile(current_user["id"])
    except Exception as e:
        logger.error(f"Get profile error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve profile"
        )

@router.put("/me", response_model=UserProfile)
async def update_profile(
    profile_data: UserProfileUpdate,
    current_user: Dict[str, Any] = Depends(get_verified_user),
    auth_service: AuthService = Depends(get_auth_service)
) -> UserProfile:
    """
    Update user profile.
    
    - **first_name**: Updated first name
    - **last_name**: Updated last name
    
    Updates user profile information.
    """
    try:
        return await auth_service.update_user_profile(current_user["id"], profile_data)
    except Exception as e:
        logger.error(f"Update profile error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Profile update failed"
        )

# ============================================================================
# API KEY MANAGEMENT ENDPOINTS
# ============================================================================

@router.post("/api-keys", response_model=ApiKeyResponse)
async def create_api_key(
    api_key_data: ApiKeyCreate,
    current_user: Dict[str, Any] = Depends(get_verified_user),
    auth_service: AuthService = Depends(get_auth_service)
) -> ApiKeyResponse:
    """
    Create new API key for programmatic access.
    
    - **key_name**: Descriptive name for the API key
    - **permissions**: List of permissions (read, write, etc.)
    - **expires_at**: Optional expiration date
    
    Returns API key details including the secret key (shown only once).
    """
    try:
        return await auth_service.create_api_key(current_user["id"], api_key_data)
    except Exception as e:
        logger.error(f"API key creation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="API key creation failed"
        )

@router.get("/api-keys", response_model=List[ApiKeyList])
async def list_api_keys(
    current_user: Dict[str, Any] = Depends(get_verified_user),
    db_pool: asyncpg.Pool = Depends(get_db_pool)
) -> List[ApiKeyList]:
    """
    List user's API keys (without secret keys).
    
    Returns list of API keys with metadata but not the actual keys.
    """
    try:
        async with db_pool.acquire() as conn:
            keys = await conn.fetch("""
                SELECT id, key_name, permissions, is_active, 
                       last_used_at, expires_at, created_at
                FROM user_api_keys 
                WHERE user_id = $1
                ORDER BY created_at DESC
            """, current_user["id"])
            
            return [ApiKeyList(**dict(key)) for key in keys]
    except Exception as e:
        logger.error(f"List API keys error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve API keys"
        )

@router.delete("/api-keys/{key_id}", response_model=AuthResponse)
async def delete_api_key(
    key_id: str,
    current_user: Dict[str, Any] = Depends(get_verified_user),
    db_pool: asyncpg.Pool = Depends(get_db_pool)
) -> AuthResponse:
    """
    Delete/deactivate API key.
    
    - **key_id**: ID of the API key to delete
    
    Deactivates the specified API key.
    """
    try:
        async with db_pool.acquire() as conn:
            result = await conn.execute("""
                UPDATE user_api_keys 
                SET is_active = false
                WHERE id = $1 AND user_id = $2
            """, key_id, current_user["id"])
            
            if result == "UPDATE 0":
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="API key not found"
                )
            
            return AuthResponse(message="API key deleted successfully")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete API key error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="API key deletion failed"
        )

# ============================================================================
# HEALTH CHECK ENDPOINT
# ============================================================================

@router.get("/health", response_model=Dict[str, Any])
async def health_check(
    db_pool: asyncpg.Pool = Depends(get_db_pool)
) -> Dict[str, Any]:
    """
    Health check for authentication service.
    
    Verifies database connectivity and service status.
    """
    try:
        async with db_pool.acquire() as conn:
            await conn.fetchval("SELECT 1")
        
        return {
            "status": "healthy",
            "service": "auth",
            "timestamp": datetime.utcnow().isoformat(),
            "version": "1.0.0"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Database connection failed: {str(e)}"
        )
