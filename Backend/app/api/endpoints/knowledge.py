"""
API endpoints for knowledge graph operations.
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Dict, List, Optional, Any

from app.core.knowledge_graph import KnowledgeGraph
from app.schemas.knowledge import (
    GraphQueryRequest,
    GraphQueryResponse,
    EntityRequest,
    RelationshipRequest
)

router = APIRouter()

@router.post("/query", response_model=GraphQueryResponse)
async def query_knowledge_graph(request: GraphQueryRequest):
    """
    Query the knowledge graph for information.
    
    This endpoint allows querying the Neo4j knowledge graph using
    natural language or Cypher queries.
    """
    try:
        kg = KnowledgeGraph()
        results = await kg.query(
            query_text=request.query,
            query_type=request.query_type,
            limit=request.limit or 10
        )
        return {"success": True, "data": results}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Knowledge graph query failed: {str(e)}")

@router.post("/entity", response_model=Dict[str, str])
async def add_entity(request: Dict[str, str]):
    """
    Add an entity to the knowledge graph.
    
    This endpoint adds a new entity node to the Neo4j knowledge graph.
    """
    try:
        kg = KnowledgeGraph()
        entity_id = await kg.add_entity(
            entity_type=request.get('entity_type'),
            properties=request.get('properties')
        )
        return {"entity_id": entity_id}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to add entity: {str(e)}")

@router.post("/relationship", response_model=Dict[str, str])
async def add_relationship(request: Dict[str, str]):
    """
    Add a relationship between entities in the knowledge graph.
    
    This endpoint creates a relationship between two entities in the Neo4j graph.
    """
    try:
        kg = KnowledgeGraph()
        relationship_id = await kg.add_relationship(
            source_id=request.get('source_id'),
            target_id=request.get('target_id'),
            relationship_type=request.get('relationship_type'),
            properties=request.get('properties')
        )
        return {"relationship_id": relationship_id}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to add relationship: {str(e)}")

@router.get("/stats", response_model=Dict[str, Any])
async def get_knowledge_graph_stats():
    """Get statistics about the knowledge graph."""
    try:
        kg = KnowledgeGraph()
        stats = await kg.get_stats()
        return {"success": True, "data": stats}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get graph statistics: {str(e)}")

# Graph visualization endpoints

@router.get("/graph/nodes")
async def get_all_nodes(
    limit: int = Query(100, le=1000),
    node_type: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    kg: KnowledgeGraph = Depends(KnowledgeGraph)
):
    """Get all nodes with metadata for visualization"""
    try:
        nodes = await kg.get_all_nodes(limit=limit, node_type=node_type, search=search)
        return {"success": True, "data": nodes}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/graph/relationships")
async def get_all_relationships(limit: int = Query(100, le=1000), kg: KnowledgeGraph = Depends(KnowledgeGraph)):
    """Get all relationships for visualization"""
    try:
        relationships = await kg.get_all_relationships(limit=limit)
        return {"success": True, "data": relationships}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/graph/full")
async def get_full_graph(limit: int = Query(50, le=200), kg: KnowledgeGraph = Depends(KnowledgeGraph)):
    """Get complete graph data for visualization"""
    try:
        nodes = await kg.get_all_nodes(limit=limit)
        links = await kg.get_all_relationships(limit=limit)

        graph_data = {
            "nodes": nodes,
            "links": links,
            "metadata": {
                "total_nodes": len(nodes),
                "total_relationships": len(links),
                "node_types": list(set(node.get("type", "Unknown") for node in nodes))
            }
        }
        
        return {"success": True, "data": graph_data}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/graph/node/{node_id}/neighbors")
async def get_node_neighbors(node_id: str, depth: int = Query(1, le=3), kg: KnowledgeGraph = Depends(KnowledgeGraph)):
    """Get neighbors of a specific node"""
    try:
        neighbor_data = await kg.get_neighbors(node_id, depth=depth)
        if not neighbor_data or not neighbor_data.get('nodes'):
             raise HTTPException(status_code=404, detail=f"Node with id {node_id} not found or has no neighbors.")
        return {"success": True, "data": neighbor_data}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/analytics/stats")
async def get_graph_analytics(kg: KnowledgeGraph = Depends(KnowledgeGraph)):
    """Get graph analytics and statistics"""
    try:
        analytics = await kg.get_graph_analytics()
        return {"success": True, "data": analytics}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))