"""
Configuration for the application.

This module provides configuration settings for the application,
including application settings and provider configuration.
"""
from .settings import Settings

# Import provider configuration if available
try:
    from .provider_config import (
        PROVIDER_SETTINGS,
        PROVIDER_TIERS,
        USER_TIER_LIMITS,
        DEFAULT_PROVIDER,
        DEFAULT_PRIORITY,
        DEFAULT_USER_TIER,
        get_provider_setting,
        get_provider_model,
        get_recommended_providers
    )
    PROVIDER_CONFIG_AVAILABLE = True
except ImportError:
    PROVIDER_CONFIG_AVAILABLE = False

# Export main classes
__all__ = ["Settings"]

# Add provider configuration exports if available
if PROVIDER_CONFIG_AVAILABLE:
    __all__.extend([
        "PROVIDER_SETTINGS",
        "PROVIDER_TIERS",
        "USER_TIER_LIMITS",
        "DEFAULT_PROVIDER",
        "DEFAULT_PRIORITY",
        "DEFAULT_USER_TIER",
        "get_provider_setting",
        "get_provider_model",
        "get_recommended_providers"
    ])

# Create a default settings instance
settings = Settings() 