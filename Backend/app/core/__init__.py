"""
Core functionality for the application.

This module provides core functionality for the application,
including AI enhancements, LLM providers, context engine, and more.
"""

# Import core modules for easy access
from .context_engine import ContextEngine
from .knowledge_graph import KnowledgeGraph
from .auth import get_password_hash, verify_password

# Import multi-provider system if available
try:
    from .llm_providers import (
        MultiProviderOrchestrator,
        SecureProviderManager,
        default_orchestrator
    )
    from .enhanced_ai_layer import EnhancedAILayer, default_ai_layer
    MULTI_PROVIDER_AVAILABLE = True
except ImportError:
    MULTI_PROVIDER_AVAILABLE = False

# Export main classes
__all__ = [
    "ContextEngine",
    "KnowledgeGraph",
    "get_password_hash",
    "verify_password",
]

# Add multi-provider exports if available
if MULTI_PROVIDER_AVAILABLE:
    __all__.extend([
        "MultiProviderOrchestrator",
        "SecureProviderManager",
        "default_orchestrator",
        "EnhancedAILayer", 
        "default_ai_layer"
    ])
