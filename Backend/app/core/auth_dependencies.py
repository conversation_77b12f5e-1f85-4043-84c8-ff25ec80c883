"""
Authentication dependencies for ConTXT API.
Provides dependency injection for user authentication and authorization.
"""
import jwt
import asyncpg
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Dict, Any, Optional
import logging

from app.config.settings import settings

logger = logging.getLogger(__name__)
security = HTTPBearer()

# Database connection dependency (to be implemented based on your DB setup)
async def get_db_pool() -> asyncpg.Pool:
    """Get database connection pool."""
    # This should return your actual database pool instance
    # You'll need to implement this based on your existing DB setup
    from app.db.postgres_client import get_db_pool as get_pool
    return await get_pool()

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db_pool: asyncpg.Pool = Depends(get_db_pool)
) -> Dict[str, Any]:
    """Get current authenticated user from JWT token."""
    try:
        payload = jwt.decode(
            credentials.credentials,
            settings.JWT_SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )
        
        if payload.get("type") != "access":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token type",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        user_id = payload.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Get user from database
        async with db_pool.acquire() as conn:
            user = await conn.fetchrow("""
                SELECT id, email, first_name, last_name, full_name, 
                       subscription_tier, is_verified, is_active,
                       created_at, last_login_at
                FROM users 
                WHERE id = $1 AND is_active = true
            """, user_id)
            
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User not found or inactive",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            return dict(user)
            
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
            headers={"WWW-Authenticate": "Bearer"},
        )

async def get_verified_user(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """Require verified user."""
    if not current_user.get('is_verified'):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Email verification required"
        )
    return current_user

async def get_premium_user(
    current_user: Dict[str, Any] = Depends(get_verified_user)
) -> Dict[str, Any]:
    """Require premium subscription."""
    if current_user.get('subscription_tier') == 'free':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Premium subscription required"
        )
    return current_user

async def get_enterprise_user(
    current_user: Dict[str, Any] = Depends(get_verified_user)
) -> Dict[str, Any]:
    """Require enterprise subscription."""
    if current_user.get('subscription_tier') != 'enterprise':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Enterprise subscription required"
        )
    return current_user

async def get_optional_user(
    request: Request,
    db_pool: asyncpg.Pool = Depends(get_db_pool)
) -> Optional[Dict[str, Any]]:
    """Get user if authenticated, otherwise return None."""
    try:
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return None
        
        token = auth_header.split(" ")[1]
        payload = jwt.decode(
            token,
            settings.JWT_SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )
        
        if payload.get("type") != "access":
            return None
        
        user_id = payload.get("sub")
        if not user_id:
            return None
        
        async with db_pool.acquire() as conn:
            user = await conn.fetchrow("""
                SELECT id, email, first_name, last_name, subscription_tier, 
                       is_verified, is_active
                FROM users 
                WHERE id = $1 AND is_active = true
            """, user_id)
            
            return dict(user) if user else None
            
    except (jwt.JWTError, Exception) as e:
        logger.debug(f"Optional auth failed: {e}")
        return None

async def verify_api_key(
    request: Request,
    db_pool: asyncpg.Pool = Depends(get_db_pool)
) -> Dict[str, Any]:
    """Verify API key authentication."""
    api_key = request.headers.get("X-API-Key")
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key required"
        )
    
    if not api_key.startswith("ctx_"):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key format"
        )
    
    async with db_pool.acquire() as conn:
        # Find API key and associated user
        result = await conn.fetchrow("""
            SELECT ak.id, ak.user_id, ak.permissions, ak.expires_at,
                   u.email, u.subscription_tier, u.is_active, u.is_verified
            FROM user_api_keys ak
            JOIN users u ON ak.user_id = u.id
            WHERE ak.api_key = $1 AND ak.is_active = true AND u.is_active = true
        """, api_key)
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid API key"
            )
        
        # Check expiration
        if result['expires_at'] and result['expires_at'] < datetime.utcnow():
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="API key expired"
            )
        
        # Update usage
        await conn.execute("""
            UPDATE user_api_keys 
            SET last_used_at = now(), usage_count = usage_count + 1
            WHERE id = $1
        """, result['id'])
        
        return {
            "user_id": str(result['user_id']),
            "email": result['email'],
            "subscription_tier": result['subscription_tier'],
            "permissions": result['permissions'],
            "auth_type": "api_key"
        }

def require_permission(permission: str):
    """Decorator to require specific permission."""
    def permission_checker(current_user: Dict[str, Any] = Depends(get_current_user)):
        # For JWT auth, assume all permissions for now
        # You can implement more granular permissions later
        return current_user
    return permission_checker

def require_api_permission(permission: str):
    """Decorator to require specific API key permission."""
    def permission_checker(api_user: Dict[str, Any] = Depends(verify_api_key)):
        if permission not in api_user.get('permissions', []):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission}' required"
            )
        return api_user
    return permission_checker

# Rate limiting helper
def get_client_ip(request: Request) -> str:
    """Get client IP address from request."""
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()
    
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip
    
    return request.client.host if request.client else "unknown"

def get_user_agent(request: Request) -> str:
    """Get user agent from request."""
    return request.headers.get("User-Agent", "unknown")
