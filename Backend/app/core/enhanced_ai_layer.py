"""
Enhanced AI layer for document processing with multi-provider support.

This module provides an enhanced AI layer that integrates with the multi-provider
LLM system, providing advanced capabilities for document processing and analysis.
"""
import os
import logging
from typing import Dict, List, Any, Optional, Union

from app.core.llm_providers import (
    MultiProviderOrchestrator, 
    default_orchestrator,
    SecureProviderManager
)

logger = logging.getLogger(__name__)

class EnhancedAILayer:
    """
    Enhanced AI layer with multi-provider support for document processing.
    
    This class extends the original AIEnhancementLayer with support for multiple
    LLM providers, intelligent provider selection, fallback mechanisms, and
    cost optimization strategies.
    """
    
    def __init__(
        self,
        orchestrator: Optional[MultiProviderOrchestrator] = None,
        default_provider: str = "openai",
        default_priority: str = "balanced",
        user_tier: str = "pro_tier"
    ):
        """
        Initialize the enhanced AI layer.
        
        Args:
            orchestrator: Multi-provider orchestrator (uses default if None)
            default_provider: Default provider to use
            default_priority: Default priority for provider selection
            user_tier: User subscription tier
        """
        # Use provided orchestrator or default
        self.orchestrator = orchestrator or default_orchestrator
        
        # Default settings
        self.default_provider = default_provider
        self.default_priority = default_priority
        self.user_tier = user_tier
        
        # Initialize embedding provider
        self.embeddings = self.orchestrator.initialize_embedding_provider(
            provider="openai",
            model="text-embedding-3-large"
        )
        
        # Track initialization status
        self.initialized = self._check_initialization()
        
        logger.info(f"Enhanced AI layer initialized: {self.initialized}")
    
    def _check_initialization(self) -> bool:
        """
        Check if the AI layer is properly initialized.
        
        Returns:
            True if initialized, False otherwise
        """
        # Check if orchestrator has available providers
        has_providers = bool(self.orchestrator.provider_clients)
        
        # Check if default provider is available
        default_available = (
            self.default_provider in self.orchestrator.provider_clients or 
            len(self.orchestrator.provider_clients) > 0
        )
        
        return has_providers and default_available
    
    async def enhance_content(
        self, 
        content: Any, 
        content_type: str, 
        enhancement_type: str = "analysis",
        provider: Optional[str] = None,
        priority: Optional[str] = None,
        task_type: Optional[str] = None
    ) -> Optional[str]:
        """
        Enhance content with AI analysis using intelligent provider selection.
        
        Args:
            content: Content to enhance
            content_type: Type of content
            enhancement_type: Type of enhancement to perform
            provider: Specific provider to use (if None, select optimal)
            priority: Priority for provider selection
            task_type: Type of task
            
        Returns:
            Enhanced content or None if enhancement fails
        """
        if not self.initialized:
            logger.warning("AI enhancement requested but AI layer is not properly initialized")
            return None
        
        # Create prompt based on content type and enhancement type
        prompt = self._create_enhancement_prompt(content, content_type, enhancement_type)
        
        # Set task type based on enhancement type if not provided
        if not task_type:
            task_type = self._map_enhancement_to_task(enhancement_type)
        
        # Use provided priority or default
        priority = priority or self.default_priority
        
        try:
            # Define provider chain if specific provider requested
            provider_chain = [provider] if provider else None
            
            # Execute with automatic fallback
            result = await self.orchestrator.execute_with_fallback(
                prompt=prompt,
                provider_chain=provider_chain,
                task_type=task_type,
                user_tier=self.user_tier,
                priority=priority
            )
            
            # Extract content from result
            if hasattr(result, "content"):
                return result.content
            else:
                return str(result)
        except Exception as e:
            logger.error(f"AI enhancement failed: {e}")
            return None
    
    def _map_enhancement_to_task(self, enhancement_type: str) -> str:
        """
        Map enhancement type to task type for provider selection.
        
        Args:
            enhancement_type: Type of enhancement
            
        Returns:
            Corresponding task type
        """
        # Map enhancement types to task types
        enhancement_to_task = {
            "analysis": "analysis",
            "summary": "summarization",
            "extract": "extraction",
            "classify": "classification",
            "generate": "generation",
            "translate": "translation",
            "research": "research"
        }
        
        return enhancement_to_task.get(enhancement_type, "chat")
    
    def _create_enhancement_prompt(self, content: Any, content_type: str, enhancement_type: str) -> str:
        """
        Create a prompt for AI enhancement.
        
        Args:
            content: Content to enhance
            content_type: Type of content
            enhancement_type: Type of enhancement
            
        Returns:
            Prompt for the AI model
        """
        # Base prompt template
        if enhancement_type == "analysis":
            return f"""
            Analyze the following {content_type} content and provide insights:
            
            {content}
            
            Extract:
            1. Key topics and themes
            2. Important entities and relationships
            3. Main insights and findings
            4. Structure and organization
            """
        elif enhancement_type == "summary":
            return f"""
            Summarize the following {content_type} content:
            
            {content}
            
            Provide a concise summary highlighting the most important information.
            """
        elif enhancement_type == "extract":
            return f"""
            Extract structured information from the following {content_type} content:
            
            {content}
            
            Provide the extracted information in a clear, structured format.
            """
        elif enhancement_type == "classify":
            return f"""
            Classify the following {content_type} content:
            
            {content}
            
            Provide the primary category, subcategories, and confidence levels.
            """
        elif enhancement_type == "research":
            return f"""
            Research the following topic from {content_type} content:
            
            {content}
            
            Provide detailed, factual information including recent developments.
            """
        else:
            return f"""
            Process the following {content_type} content for {enhancement_type}:
            
            {content}
            """
    
    async def generate_embeddings(self, text: str, model: str = "text-embedding-3-large") -> Optional[List[float]]:
        """
        Generate embeddings for text using optimal provider.
        
        Args:
            text: Text to generate embeddings for
            model: Embedding model to use
            
        Returns:
            Vector embedding or None if generation fails
        """
        if not self.embeddings:
            logger.warning("Embedding generation requested but no embedding provider is available")
            return None
        
        try:
            # Generate embeddings
            result = await self.embeddings.aembed_query(text)
            return result
        except Exception as e:
            logger.error(f"Embedding generation failed: {e}")
            return None
    
    async def analyze_document(
        self, 
        content: str, 
        content_type: str, 
        analysis_type: str = "comprehensive"
    ) -> Dict[str, Any]:
        """
        Perform advanced document analysis.
        
        Args:
            content: Document content
            content_type: Type of content
            analysis_type: Type of analysis to perform
            
        Returns:
            Analysis results
        """
        if not self.initialized:
            logger.warning("Document analysis requested but AI layer is not properly initialized")
            return {"error": "AI layer not initialized"}
        
        # Create prompt based on analysis type
        if analysis_type == "comprehensive":
            prompt = f"""
            Perform comprehensive analysis on the following {content_type} document:
            
            {content}
            
            Provide:
            1. Executive summary (2-3 sentences)
            2. Key topics and themes (bullet points)
            3. Important entities mentioned (names, organizations, dates, etc.)
            4. Main insights and claims
            5. Document structure analysis
            6. Sentiment and tone assessment
            7. Knowledge domains covered
            
            Format the response as JSON with these sections as keys.
            """
        elif analysis_type == "technical":
            prompt = f"""
            Perform technical analysis on the following {content_type} document:
            
            {content}
            
            Provide:
            1. Technical summary
            2. Technologies mentioned
            3. Technical concepts explained
            4. Code elements or algorithms
            5. Technical accuracy assessment
            6. Implementation considerations
            
            Format the response as JSON with these sections as keys.
            """
        else:
            prompt = f"""
            Analyze the following {content_type} document for {analysis_type}:
            
            {content}
            
            Provide a comprehensive analysis focused on {analysis_type}.
            Format the response as JSON with relevant sections.
            """
        
        try:
            # Select optimal provider for this task (analysis tasks benefit from higher quality)
            result = await self.orchestrator.execute_with_fallback(
                prompt=prompt,
                task_type="analysis",
                priority="quality"
            )
            
            # Extract content from result
            content = result.content if hasattr(result, "content") else str(result)
            
            # Try to parse JSON (if model didn't return proper JSON, wrap in dict)
            try:
                import json
                return json.loads(content)
            except json.JSONDecodeError:
                return {"raw_analysis": content}
        except Exception as e:
            logger.error(f"Document analysis failed: {e}")
            return {"error": str(e)}
    
    def get_provider_health(self) -> Dict[str, Dict[str, Any]]:
        """
        Get health status of all providers.
        
        Returns:
            Dictionary of provider health status
        """
        return self.orchestrator.provider_manager.get_provider_health()
    
    def get_available_providers(self) -> List[str]:
        """
        Get list of available providers.
        
        Returns:
            List of available provider names
        """
        return list(self.orchestrator.provider_clients.keys())


# Create a default instance for convenience
default_ai_layer = EnhancedAILayer() 