"""
Example usage of the multi-provider LLM system.

This module demonstrates how to use the multi-provider LLM system
for different document processing tasks.
"""
import os
import asyncio
import logging
from typing import Dict, Any

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import the components we'll use
from processors.factory import ProcessorFactory
from processors.processors_updated import MultiProviderProcessor
from core.enhanced_ai_layer import EnhancedAILayer, default_ai_layer
from core.llm_providers import MultiProviderOrchestrator, default_orchestrator
from app.config.provider_config import get_recommended_providers

async def process_document_example():
    """Example of processing a document with multi-provider support."""
    # Create a processor with multi-provider support
    processor = ProcessorFactory.get_enhanced_processor(
        content_type="text/plain",
        use_multi_provider=True,
        dataset_name="example_dataset",
        user_tier="pro_tier",
        priority="balanced"
    )
    
    # Sample content to process
    content = """
    # Machine Learning Project Proposal
    
    ## Overview
    This project aims to develop a predictive model for customer churn in the telecommunications industry.
    
    ## Objectives
    1. Identify key factors leading to customer churn
    2. Develop a model with at least 85% accuracy
    3. Implement the model in a production-ready API
    
    ## Timeline
    - Data collection and preparation: 2 weeks
    - Model development and testing: 4 weeks
    - API development and deployment: 2 weeks
    
    ## Budget
    Total estimated cost: $50,000
    """
    
    # Process the content with enhancements
    result = await processor.process_with_enhancements(
        content=content,
        enhancement_type="analysis",
        priority="quality"  # Use high-quality models for this analysis
    )
    
    # Print the results
    logger.info("Document processing results:")
    logger.info(f"Processed content: {result.get('processed_content', '')[:100]}...")
    logger.info(f"Has enhancements: {result.get('has_enhancements', False)}")
    
    if result.get("has_enhancements"):
        logger.info("Enhanced content:")
        logger.info(result.get("enhanced_content", ""))
    
    # Print provider information if available
    if "provider_info" in result:
        logger.info("Provider information:")
        logger.info(f"Available providers: {result['provider_info'].get('available_providers', [])}")
        logger.info(f"Recommended providers: {result['provider_info'].get('recommended_providers', [])}")
    
    return result

async def try_different_providers():
    """Example of trying different providers for the same task."""
    # Initialize the AI layer directly
    ai_layer = EnhancedAILayer()
    
    # Content to analyze
    content = "Climate change is one of the most pressing issues of our time. It requires coordinated global action."
    
    # Try different providers and priorities
    providers_to_try = {
        "openai": "quality",
        "anthropic": "quality",
        "google": "balanced",
        "groq": "speed",
        "ollama": "speed"
    }
    
    results = {}
    for provider, priority in providers_to_try.items():
        try:
            logger.info(f"Trying provider: {provider} with priority: {priority}")
            
            # Only try if provider is available
            if provider in ai_layer.get_available_providers():
                result = await ai_layer.enhance_content(
                    content=content,
                    content_type="text/plain",
                    enhancement_type="summary",
                    provider=provider,
                    priority=priority
                )
                
                results[provider] = result
                logger.info(f"Result from {provider}: {result}")
            else:
                logger.info(f"Provider {provider} not available")
        except Exception as e:
            logger.error(f"Error with provider {provider}: {e}")
    
    return results

async def run_document_analysis():
    """Example of running advanced document analysis."""
    # Create a multi-provider processor directly
    processor = MultiProviderProcessor(
        enable_ai=True,
        user_tier="enterprise",
        priority="quality"
    )
    
    # Sample document to analyze
    document = """
    QUARTERLY FINANCIAL REPORT - Q2 2023
    
    Revenue: $12.4 million
    Growth: 15% year-over-year
    New customers: 2,500
    Churn rate: 3.2%
    
    Key Highlights:
    1. Launched premium subscription tier, contributing $1.2M in new revenue
    2. Expanded European operations with new office in Berlin
    3. Reduced customer acquisition cost by 12%
    
    Challenges:
    - Supply chain disruptions affected hardware product delivery
    - Increased competition in North American market
    
    Outlook:
    We expect continued growth in Q3, with projected revenue of $13.5-14.5 million.
    """
    
    # Run advanced analysis
    result = await processor.analyze_document(
        content=document,
        content_type="text/plain",
        analysis_type="comprehensive"
    )
    
    # Print the results
    logger.info("Document analysis results:")
    logger.info(result)
    
    return result

async def test_provider_fallback():
    """Example of testing the provider fallback mechanism."""
    # Get orchestrator directly
    orchestrator = default_orchestrator
    
    # Test prompt
    prompt = "Explain quantum computing in simple terms."
    
    # Try with specific provider chain (intentionally including some that might not be available)
    try:
        logger.info("Testing fallback mechanism with specific provider chain")
        result = await orchestrator.execute_with_fallback(
            prompt=prompt,
            provider_chain=["unavailable_provider", "openai", "anthropic", "groq", "ollama"],
            task_type="chat"
        )
        
        if hasattr(result, "content"):
            logger.info(f"Result content: {result.content[:100]}...")
        else:
            logger.info(f"Result: {str(result)[:100]}...")
        
        # Show which provider succeeded
        provider_health = orchestrator.provider_manager.get_provider_health()
        for provider, status in provider_health.items():
            if status.get("healthy"):
                logger.info(f"Provider {provider} is healthy")
            else:
                logger.info(f"Provider {provider} has failures: {status.get('failure_count', 0)}")
    
    except Exception as e:
        logger.error(f"Fallback test failed: {e}")
    
    return orchestrator.provider_manager.get_provider_health()

async def main():
    """Run all examples."""
    logger.info("Starting multi-provider examples")
    
    # Process a document
    logger.info("\n\n=== DOCUMENT PROCESSING EXAMPLE ===\n")
    await process_document_example()
    
    # Try different providers
    logger.info("\n\n=== DIFFERENT PROVIDERS EXAMPLE ===\n")
    await try_different_providers()
    
    # Run document analysis
    logger.info("\n\n=== DOCUMENT ANALYSIS EXAMPLE ===\n")
    await run_document_analysis()
    
    # Test provider fallback
    logger.info("\n\n=== PROVIDER FALLBACK EXAMPLE ===\n")
    await test_provider_fallback()
    
    logger.info("\n\nAll examples completed")

if __name__ == "__main__":
    # Ensure required environment variables are set
    required_vars = [
        "OPENAI_API_KEY",  # At least one provider key should be present
        "OPENROUTER_API_KEY"
    ]
    
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    if missing_vars:
        logger.warning(f"Missing environment variables: {missing_vars}")
        logger.warning("Some examples may fail without required API keys")
    
    # Run the examples
    asyncio.run(main()) 