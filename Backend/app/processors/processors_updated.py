"""
Updated document processor implementation with multi-provider LLM support.

This module provides an enhanced version of the BaseProcessor class 
that leverages the multi-provider LLM system for intelligent processing.
"""
import logging
import os
from typing import Dict, List, Any, Optional, Union

from app.processors.base import BaseProcessor
from app.core.enhanced_ai_layer import Enhanced<PERSON><PERSON>ayer, default_ai_layer
from app.config.provider_config import (
    DEFAULT_PRIORITY,
    DEFAULT_USER_TIER,
    get_recommended_providers
)

logger = logging.getLogger(__name__)

class MultiProviderProcessor(BaseProcessor):
    """
    Enhanced document processor with multi-provider LLM support.
    
    This class extends the base processor with support for multiple LLM providers,
    intelligent provider selection, fallback mechanisms, and advanced AI capabilities.
    """
    
    def __init__(
        self,
        dataset_name: str = None,
        use_cognee: bool = False,
        enable_ai: bool = True,
        ai_layer: Optional[EnhancedAILayer] = None,
        user_tier: str = DEFAULT_USER_TIER,
        priority: str = DEFAULT_PRIORITY
    ):
        """
        Initialize the multi-provider processor.
        
        Args:
            dataset_name: Name of the dataset (for Cognee integration)
            use_cognee: Whether to use Cognee for database operations
            enable_ai: Whether to enable AI enhancements
            ai_layer: Enhanced AI layer to use (uses default if None)
            user_tier: User subscription tier
            priority: Priority for provider selection
        """
        # Initialize base processor
        super().__init__(
            dataset_name=dataset_name,
            use_cognee=use_cognee,
            enable_ai=enable_ai
        )
        
        # Set up enhanced AI layer
        self.enable_ai = enable_ai
        if enable_ai:
            self.ai_layer = ai_layer or default_ai_layer
        
        # Set user tier and priority
        self.user_tier = user_tier
        self.priority = priority
        
        # Additional metadata
        self.add_metadata("processor_type", "multi_provider")
        self.add_metadata("user_tier", user_tier)
        self.add_metadata("priority", priority)
    
    async def process_with_enhancements(self, content: Any, **kwargs) -> Dict[str, Any]:
        """
        Process the document content with AI enhancements using optimal provider.
        
        Args:
            content: Document content to process
            **kwargs: Additional processing options
            
        Returns:
            Enhanced processing result
        """
        # Standard processing first
        result = await self.process(content, **kwargs)
        
        # Apply AI enhancements if enabled
        if self.enable_ai and hasattr(self, "ai_layer"):
            content_type = kwargs.get("content_type") or self._detect_content_type(content)
            enhancement_type = kwargs.get("enhancement_type", "analysis")
            
            # Determine optimal provider based on task type
            task_type = self._map_enhancement_to_task(enhancement_type)
            provider = kwargs.get("provider")
            priority = kwargs.get("priority", self.priority)
            
            # Enhanced content processing
            enhanced_content = await self.ai_layer.enhance_content(
                content=result.get("processed_content", content),
                content_type=content_type,
                enhancement_type=enhancement_type,
                provider=provider,
                priority=priority,
                task_type=task_type
            )
            
            if enhanced_content:
                result["enhanced_content"] = enhanced_content
                result["has_enhancements"] = True
                
                # Add provider info to metadata if available
                provider_health = self.ai_layer.get_provider_health()
                if provider_health:
                    available_providers = self.ai_layer.get_available_providers()
                    result["provider_info"] = {
                        "available_providers": available_providers,
                        "recommended_providers": get_recommended_providers(task_type, self.user_tier)
                    }
        
        return result
    
    def _map_enhancement_to_task(self, enhancement_type: str) -> str:
        """
        Map enhancement type to task type for provider selection.
        
        Args:
            enhancement_type: Type of enhancement
            
        Returns:
            Corresponding task type
        """
        # Map enhancement types to task types
        enhancement_to_task = {
            "analysis": "analysis",
            "summary": "summarization",
            "extract": "extraction",
            "classify": "classification",
            "generate": "generation",
            "translate": "translation",
            "research": "research"
        }
        
        return enhancement_to_task.get(enhancement_type, "chat")
    
    async def generate_embeddings(self, text: str, model: str = "text-embedding-3-large") -> List[float]:
        """
        Generate embeddings for text using optimal provider.
        
        Args:
            text: Text to generate embeddings for
            model: Embedding model to use
            
        Returns:
            Vector embedding
        """
        # Use enhanced AI layer for embeddings if available
        if self.enable_ai and hasattr(self, "ai_layer"):
            embeddings = await self.ai_layer.generate_embeddings(text, model)
            if embeddings:
                return embeddings
        
        # Fall back to base implementation
        return await super().generate_embeddings(text, model)
    
    async def analyze_document(self, content: str, content_type: str, analysis_type: str = "comprehensive") -> Dict[str, Any]:
        """
        Perform advanced document analysis using optimal provider.
        
        Args:
            content: Document content
            content_type: Type of content
            analysis_type: Type of analysis to perform
            
        Returns:
            Analysis results
        """
        if not self.enable_ai or not hasattr(self, "ai_layer"):
            logger.warning("Document analysis requested but AI is not enabled")
            return {"error": "AI not enabled"}
        
        # Use enhanced AI layer for document analysis
        return await self.ai_layer.analyze_document(
            content=content,
            content_type=content_type,
            analysis_type=analysis_type
        )
    
    def get_available_providers(self) -> List[str]:
        """
        Get list of available providers.
        
        Returns:
            List of available provider names
        """
        if self.enable_ai and hasattr(self, "ai_layer"):
            return self.ai_layer.get_available_providers()
        return []
    
    def get_provider_health(self) -> Dict[str, Dict[str, Any]]:
        """
        Get health status of all providers.
        
        Returns:
            Dictionary of provider health status
        """
        if self.enable_ai and hasattr(self, "ai_layer"):
            return self.ai_layer.get_provider_health()
        return {} 