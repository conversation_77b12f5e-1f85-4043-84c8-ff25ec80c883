"""
Authentication schemas for ConTXT API.
"""
from pydantic import BaseModel, EmailStr, validator, Field, constr
from typing import Optional, List, Dict, Any
from datetime import datetime
import uuid
import re

class UserRegistration(BaseModel):
    """User registration request schema."""
    email: EmailStr
    password: str = Field(..., min_length=8)
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    
    @validator('password')
    def validate_password(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v

class UserLogin(BaseModel):
    """User login request schema."""
    email: EmailStr
    password: str

class TokenResponse(BaseModel):
    """JWT token response schema."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user_id: str

class RefreshTokenRequest(BaseModel):
    """Refresh token request schema."""
    refresh_token: str

class UserProfile(BaseModel):
    """User profile response schema."""
    id: str
    email: str
    first_name: Optional[str]
    last_name: Optional[str]
    full_name: Optional[str]
    is_active: bool
    is_verified: bool
    subscription_tier: str
    created_at: datetime
    updated_at: Optional[datetime]

class UserProfileUpdate(BaseModel):
    """User profile update request schema."""
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)

class PasswordChange(BaseModel):
    """Password change request schema."""
    current_password: str
    new_password: str = Field(..., min_length=8)
    
    @validator('new_password')
    def validate_new_password(cls, v):
        """Validate new password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v

class PasswordReset(BaseModel):
    """Password reset request schema."""
    token: str
    new_password: str = Field(..., min_length=8)
    
    @validator('new_password')
    def validate_new_password(cls, v):
        """Validate new password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v

class OTPVerification(BaseModel):
    """OTP verification request schema."""
    email: EmailStr
    otp_code: str = Field(..., min_length=6, max_length=6)

class ForgotPasswordRequest(BaseModel):
    """Forgot password request schema."""
    email: EmailStr

class ApiKeyCreate(BaseModel):
    """API key creation request schema."""
    key_name: str = Field(..., max_length=100)
    permissions: List[str] = Field(default=["read"])
    expires_at: Optional[datetime] = None

class ApiKeyResponse(BaseModel):
    """API key response schema."""
    id: str
    key_name: str
    api_key: str
    permissions: List[str]
    is_active: bool
    expires_at: Optional[datetime]
    created_at: datetime

class ApiKeyList(BaseModel):
    """API key list response schema."""
    api_keys: List[ApiKeyResponse]

class OTPRequest(BaseModel):
    """OTP request schema."""
    email: EmailStr

class OTPVerificationNew(BaseModel):
    """OTP verification request schema."""
    email: EmailStr
    otp_code: str

    @validator('otp_code')
    def validate_otp_code(cls, v):
        if not re.match(r'^\d{6}$', v):
            raise ValueError('OTP must be exactly 6 digits')
        return v

class UserRegistrationOTP(UserRegistration):
    """User registration with OTP request schema."""
    otp_code: str

    @validator('otp_code')
    def validate_otp_code(cls, v):
        if not re.match(r'^\d{6}$', v):
            raise ValueError('OTP must be exactly 6 digits')
        return v

class PasswordResetOTP(BaseModel):
    """Password reset with OTP request schema."""
    email: EmailStr
    otp_code: str
    new_password: str

    @validator('otp_code')
    def validate_otp_code(cls, v):
        if not re.match(r'^\d{6}$', v):
            raise ValueError('OTP must be exactly 6 digits')
        return v

class OTPResponse(BaseModel):
    """OTP response schema."""
    success: bool
    message: str
    expires_in: Optional[int] = None
    created_at: datetime

class UserStats(BaseModel):
    """User statistics schema."""
    total_contexts_created: int
    total_api_calls: int
    subscription_tier: str
    account_created: datetime
    last_login: Optional[datetime]

class AuthResponse(BaseModel):
    """Generic auth response schema."""
    message: str
    success: bool = True
    data: Optional[Dict[str, Any]] = None

class ErrorResponse(BaseModel):
    """Error response schema."""
    message: str
    success: bool = False
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
