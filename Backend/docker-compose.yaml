services:
  # Main Application
  app:
    build: .
    container_name: contxt_app
    ports:
      - "8000:8000"
    env_file:
      - ./.env
    depends_on:
      - neo4j
      - qdrant
      - postgres
      - redis
    environment:
      # API Configuration
      - XAI_API_KEY=${XAI_API_KEY}
      - LLM_API_KEY=${LLM_API_KEY:-${XAI_API_KEY}}
      - LLM_PROVIDER=${LLM_PROVIDER:-openai}
      - LLM_MODEL=${LLM_MODEL:-grok-beta}
      - LLM_ENDPOINT=${LLM_ENDPOINT:-https://api.x.ai/v1}
      
      # Database Configuration
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=${NEO4J_PASSWORD:-password}
      - GRAPH_DATABASE_PROVIDER=neo4j
      
      - VECTOR_DB_PROVIDER=qdrant
      - VECTOR_DB_URL=http://qdrant:6333
      
      - DB_PROVIDER=postgres
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=${DB_PASSWORD:-postgres}
      - DB_NAME=${DB_NAME:-document_processor}
      - DB_SSL_MODE=disable
      
      - REDIS_URL=redis://redis:6379
      
      # Context Engineering Configuration
      - MAX_CONTEXT_WINDOW=128000
      - COMPRESSION_RATIO=0.5
      - CHUNK_SIZE=1024
      - CHUNK_OVERLAP=128
      - EMBEDDING_MODEL=${EMBEDDING_MODEL:-text-embedding-3-large}
      
      # FastAPI Configuration
      - DEBUG=${DEBUG:-true}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - CORS_ORIGINS=${CORS_ORIGINS:-["http://localhost:3000", "http://localhost:8000", "http://localhost:5173"]}
      
      # File Processing
      - MAX_FILE_SIZE=${MAX_FILE_SIZE:-104857600}
      - UPLOAD_PATH=/app/uploads
      - PROCESSED_PATH=/app/processed
      - MAX_CONCURRENT_UPLOADS=${MAX_CONCURRENT_UPLOADS:-10}
      - SUPPORTED_FILE_TYPES=${SUPPORTED_FILE_TYPES:-["json", "csv", "txt", "md", "pdf", "png", "jpg", "jpeg"]}
      
      # Security
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here-make-it-long-and-random}
      - PYTHONPATH=/app
      
      # Email Service
      - RESEND_API_KEY=${RESEND_API_KEY}
      
    volumes:
      - ./uploads:/app/uploads
      - ./processed:/app/processed
      - ./logs:/app/logs
      - .:/app
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    networks:
      - app-network
    restart: unless-stopped

  # Neo4j Graph Database
  neo4j:
    image: neo4j:5
    container_name: contxt_neo4j
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
    environment:
      - NEO4J_AUTH=neo4j/${NEO4J_PASSWORD:-password}
      - NEO4J_dbms_memory_pagecache_size=2G
      - NEO4J_dbms.memory.heap.initial_size=2G
      - NEO4J_dbms_memory_heap_max__size=4G
      - NEO4J_dbms_usage__report_enabled=false
      - NEO4J_PLUGINS=["apoc"]
    networks:
      - app-network
    restart: unless-stopped

  # Qdrant Vector Database
  qdrant:
    image: qdrant/qdrant:latest
    container_name: contxt_qdrant
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    networks:
      - app-network
    restart: unless-stopped

  # PostgreSQL for Metadata
  postgres:
    image: postgres:15
    container_name: contxt_postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=${DB_NAME:-document_processor}
      - POSTGRES_USER=${DB_USERNAME:-postgres}
      - POSTGRES_PASSWORD=${DB_PASSWORD:-postgres}
    networks:
      - app-network
    restart: unless-stopped

  # PostgreSQL for SaaS
  saas_postgres:
    image: postgres:13
    container_name: saas_postgres
    ports:
      - "5433:5432"
    volumes:
      - saas_postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=Saas-DB
      - POSTGRES_USER=Mewtwo
      - POSTGRES_PASSWORD=Mewtwo#0075
    networks:
      - app-network
    restart: unless-stopped

  # Redis for Caching
  redis:
    image: redis:7-alpine
    container_name: contxt_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server ${REDIS_PASSWORD:+--requirepass ${REDIS_PASSWORD}}
    networks:
      - app-network
    restart: unless-stopped

  # Celery Worker (Optional for background processing)
  worker:
    build: .
    depends_on:
      - redis
      - postgres
      - neo4j
      - qdrant
    environment:
      - XAI_API_KEY=${XAI_API_KEY}
      - LLM_API_KEY=${LLM_API_KEY:-${XAI_API_KEY}}
      - LLM_PROVIDER=${LLM_PROVIDER:-openai}
      - LLM_MODEL=${LLM_MODEL:-grok-beta}
      - LLM_ENDPOINT=${LLM_ENDPOINT:-https://api.x.ai/v1}
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=${NEO4J_PASSWORD:-password}
      - VECTOR_DB_PROVIDER=qdrant
      - VECTOR_DB_URL=http://qdrant:6333
      - DB_HOST=postgres
      - DB_USERNAME=${DB_USERNAME:-postgres}
      - DB_PASSWORD=${DB_PASSWORD:-postgres}
      - DB_NAME=${DB_NAME:-document_processor}
      - REDIS_URL=redis://redis:6379
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - CHUNK_SIZE=${CHUNK_SIZE:-1024}
      - CHUNK_OVERLAP=${CHUNK_OVERLAP:-128}
      - DEBUG=${DEBUG:-false}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - PYTHONPATH=/app
    volumes:
      - ./uploads:/app/uploads
      - ./processed:/app/processed
      - .:/app
    command: celery -A app.core.worker worker --loglevel=${LOG_LEVEL:-INFO}
    networks:
      - app-network
    restart: unless-stopped

  # Flower for Celery Monitoring (Optional)
  flower:
    build: .
    container_name: contxt_flower
    depends_on:
      - redis
      - worker
    ports:
      - "5555:5555"
    environment:
      - REDIS_URL=redis://redis:6379
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - PYTHONPATH=/app
    volumes:
      - ./uploads:/app/uploads
      - ./processed:/app/processed
      - .:/app
    command: celery -A app.core.worker flower --port=5555
    networks:
      - app-network
    restart: unless-stopped


volumes:
  neo4j_data: {}
  neo4j_logs: {}
  neo4j_import: {}
  qdrant_data: {}
  postgres_data: {}
  saas_postgres_data: {}
  redis_data: {}

networks:
  app-network:
    driver: bridge
