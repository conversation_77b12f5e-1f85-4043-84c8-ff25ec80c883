# Fly.io deployment configuration for ConTXT Backend with Authentication
app = "contxt-backend"
primary_region = "ord"
kill_signal = "SIGINT"
kill_timeout = 5

[build]
  dockerfile = "Dockerfile"

[env]
  PORT = "8000"
  ENV = "production"
  DEBUG = "false"
  LOG_LEVEL = "info"
  
  # Database URLs will be set via secrets
  # NEO4J_URI will be set via secrets
  # QDRANT_URL will be set via secrets
  # REDIS_URL will be set via secrets
  
  # Rate limiting
  RATE_LIMIT_ENABLED = "true"
  LOGIN_RATE_LIMIT = "5/minute"
  REGISTER_RATE_LIMIT = "3/hour"
  PASSWORD_RESET_RATE_LIMIT = "3/hour"
  
  # JWT Configuration
  ACCESS_TOKEN_EXPIRE_MINUTES = "30"
  REFRESH_TOKEN_EXPIRE_DAYS = "7"
  JWT_ALGORITHM = "HS256"
  
  # Email Configuration
  EMAIL_PROVIDER = "sendgrid"
  SENDGRID_FROM_EMAIL = "<EMAIL>"

[experimental]
  auto_rollback = true

[[services]]
  protocol = "tcp"
  internal_port = 8000
  processes = ["app"]

  [[services.ports]]
    port = 80
    handlers = ["http"]
    force_https = true

  [[services.ports]]
    port = 443
    handlers = ["tls", "http"]

  [services.concurrency]
    type = "connections"
    hard_limit = 100
    soft_limit = 80

  [[services.tcp_checks]]
    interval = "15s"
    timeout = "2s"
    grace_period = "5s"
    method = "get"
    path = "/auth/health"

  [[services.http_checks]]
    interval = "30s"
    timeout = "5s"
    grace_period = "10s"
    method = "get"
    path = "/auth/health"
    headers = {}

[metrics]
  port = 9090
  path = "/metrics"

# PostgreSQL database
[[services]]
  protocol = "tcp"
  internal_port = 5432
  processes = ["postgres"]

# Redis for caching and sessions
[[services]]
  protocol = "tcp"
  internal_port = 6379
  processes = ["redis"]

# Environment-specific overrides
[deploy]
  release_command = "python -m alembic upgrade head"  # Run database migrations

# Secrets to set via fly CLI:
# fly secrets set JWT_SECRET_KEY=$(openssl rand -base64 32)
# fly secrets set DATABASE_URL="********************************/contxt"
# fly secrets set NEO4J_URI="bolt://user:pass@host:7687"
# fly secrets set QDRANT_URL="http://host:6333"
# fly secrets set REDIS_URL="redis://host:6379/0"
# fly secrets set SENDGRID_API_KEY="your-sendgrid-key"
# fly secrets set OPENAI_API_KEY="your-openai-key"
# fly secrets set ANTHROPIC_API_KEY="your-anthropic-key"
