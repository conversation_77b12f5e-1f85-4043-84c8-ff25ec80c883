# Core dependencies
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
resend
python-dotenv>=1.0.0

# Security
python-jose[cryptography]
passlib[bcrypt]
bcrypt>=4.0.1
PyJWT>=2.8.0

# Rate Limiting
slowapi
redis

# Database
SQLAlchemy
psycopg2-binary
asyncpg>=0.29.0

pydantic>=2.4.2
httpx>=0.25.0

# Background task processing
celery>=5.3.4
flower>=2.0.1

# LangGraph and LLM utilities
langgraph>=0.1.11
langchain-core>=0.2.22
langchain>=0.0.335
langchain-community>=0.0.27

# LangChain Provider Integrations
langchain-openai>=0.0.5  # OpenAI and OpenRouter
langchain-anthropic>=0.1.1  # Anthropic Claude
langchain-google-genai>=0.1.1  # Google Gemini
langchain-xai>=0.0.1  # Grok API
langchain-groq>=0.1.0  # Groq fast inference
langchain-mistralai>=0.1.12  # Mistral AI
langchain-together>=0.1.0  # Together AI
langchain-perplexity>=0.0.1  # Perplexity
langchain-pinecone>=0.1.2  # Pinecone for vector store

# Vector database clients
qdrant-client>=1.6.4
neo4j>=5.13.0

# Data processing
numpy>=1.26.1
pandas>=2.1.2

# Async support
asyncio>=3.4.3
aiohttp>=3.8.6

# Document processing
Pillow>=10.1.0  # Image processing
pytesseract>=0.3.10  # OCR for images
PyMuPDF>=1.23.7  # PDF processing (fitz)
pdfminer.six>=20221105  # Alternative PDF processing
beautifulsoup4>=4.12.2  # HTML processing
html2text>=2020.1.16  # HTML to text conversion
pygments>=2.16.1  # Syntax highlighting for code

# Testing
pytest>=7.4.3
pytest-asyncio>=0.21.1

# Email Services
resend>=0.7.0

