import os
import resend

# It's better to get the key from an environment variable
api_key = os.environ.get("RESEND_API_KEY")

if not api_key:
    print("Error: RESEND_API_KEY environment variable not set.")
    exit(1)

resend.api_key = api_key

params = {
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Test Email from ConTXT Backend",
    "html": "<strong>This is a test email to verify resend functionality.</strong>",
}

try:
    print("Attempting to send email...")
    email = resend.Emails.send(params)
    print("Email sent successfully!")
    print(email)
except Exception as e:
    print(f"Failed to send email. Error: {e}")
