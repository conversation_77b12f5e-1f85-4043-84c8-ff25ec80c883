"""
Load testing for ConTXT authentication endpoints using Locust.
Tests authentication system under concurrent load.
"""
from locust import HttpUser, task, between
import random
import string
import json

class AuthLoadTestUser(HttpUser):
    """Load test user for authentication endpoints."""
    
    wait_time = between(1, 3)
    
    def on_start(self):
        """Setup user data for load testing."""
        self.email = f"loadtest_{random.randint(1000, 9999)}@example.com"
        self.password = "LoadTest123!"
        self.access_token = None
        self.user_id = None
    
    @task(1)
    def register_user(self):
        """Test user registration under load."""
        response = self.client.post("/auth/register", json={
            "email": self.email,
            "password": self.password,
            "first_name": "Load",
            "last_name": "Test"
        })
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success") and "user_id" in data.get("data", {}):
                self.user_id = data["data"]["user_id"]
                print(f"✓ Registered user: {self.email}")
        elif response.status_code == 429:
            print(f"⚠ Rate limited during registration: {self.email}")
        else:
            print(f"✗ Registration failed for {self.email}: {response.status_code}")
    
    @task(3)
    def login_user(self):
        """Test user login under load."""
        if not self.access_token:
            response = self.client.post("/auth/login", json={
                "email": self.email,
                "password": self.password
            })
            
            if response.status_code == 200:
                data = response.json()
                self.access_token = data.get("access_token")
                self.user_id = data.get("user_id")
                print(f"✓ Logged in user: {self.email}")
            elif response.status_code == 429:
                print(f"⚠ Rate limited during login: {self.email}")
            elif response.status_code == 401:
                # User might not exist yet, try to register
                self.register_user()
            else:
                print(f"✗ Login failed for {self.email}: {response.status_code}")
    
    @task(2)
    def access_profile(self):
        """Test accessing protected profile endpoint."""
        if self.access_token:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            response = self.client.get("/auth/me", headers=headers)
            
            if response.status_code == 200:
                print(f"✓ Profile accessed for: {self.email}")
            elif response.status_code == 401:
                # Token might be expired
                self.access_token = None
                print(f"⚠ Token expired for: {self.email}")
            else:
                print(f"✗ Profile access failed for {self.email}: {response.status_code}")
    
    @task(1)
    def update_profile(self):
        """Test profile update under load."""
        if self.access_token:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            response = self.client.put("/auth/me", 
                headers=headers,
                json={
                    "first_name": f"Updated{random.randint(1, 100)}",
                    "last_name": "LoadTest"
                }
            )
            
            if response.status_code == 200:
                print(f"✓ Profile updated for: {self.email}")
            elif response.status_code == 401:
                self.access_token = None
                print(f"⚠ Token expired during profile update: {self.email}")
    
    @task(1)
    def test_password_reset_request(self):
        """Test password reset request under load."""
        response = self.client.post("/auth/forgot-password", json={
            "email": self.email
        })
        
        if response.status_code == 200:
            print(f"✓ Password reset requested for: {self.email}")
        elif response.status_code == 429:
            print(f"⚠ Rate limited during password reset: {self.email}")
    
    @task(1)
    def create_api_key(self):
        """Test API key creation under load."""
        if self.access_token:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            response = self.client.post("/auth/api-keys",
                headers=headers,
                json={
                    "key_name": f"LoadTest-{random.randint(1, 1000)}",
                    "permissions": ["read", "write"]
                }
            )
            
            if response.status_code == 200:
                print(f"✓ API key created for: {self.email}")
            elif response.status_code == 401:
                self.access_token = None
    
    @task(1)
    def list_api_keys(self):
        """Test API key listing under load."""
        if self.access_token:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            response = self.client.get("/auth/api-keys", headers=headers)
            
            if response.status_code == 200:
                print(f"✓ API keys listed for: {self.email}")
            elif response.status_code == 401:
                self.access_token = None
    
    @task(1)
    def health_check(self):
        """Test health check endpoint."""
        response = self.client.get("/auth/health")
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "healthy":
                print("✓ Auth service healthy")
        else:
            print(f"✗ Health check failed: {response.status_code}")

class HighVolumeAuthUser(HttpUser):
    """High-volume authentication testing."""
    
    wait_time = between(0.1, 0.5)  # Much faster requests
    
    def on_start(self):
        """Setup for high-volume testing."""
        self.email = f"highvol_{random.randint(10000, 99999)}@example.com"
        self.password = "HighVol123!"
        self.access_token = None
    
    @task(5)
    def rapid_login_attempts(self):
        """Test rapid login attempts."""
        response = self.client.post("/auth/login", json={
            "email": self.email,
            "password": self.password
        })
        
        if response.status_code == 429:
            print("✓ Rate limiting working correctly")
        elif response.status_code == 401:
            # Expected for non-existent users
            pass
    
    @task(2)
    def rapid_registration_attempts(self):
        """Test rapid registration attempts."""
        unique_email = f"rapid_{random.randint(100000, 999999)}@example.com"
        response = self.client.post("/auth/register", json={
            "email": unique_email,
            "password": self.password
        })
        
        if response.status_code == 429:
            print("✓ Registration rate limiting working")

class SecurityTestUser(HttpUser):
    """Security-focused load testing."""
    
    wait_time = between(0.5, 2)
    
    @task(1)
    def test_invalid_tokens(self):
        """Test with invalid JWT tokens."""
        invalid_tokens = [
            "invalid.token.here",
            "Bearer fake_token",
            "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.invalid",
            ""
        ]
        
        for token in invalid_tokens:
            headers = {"Authorization": f"Bearer {token}"}
            response = self.client.get("/auth/me", headers=headers)
            
            if response.status_code == 401:
                print("✓ Invalid token properly rejected")
            else:
                print(f"✗ Security issue: Invalid token accepted: {response.status_code}")
    
    @task(1)
    def test_sql_injection_attempts(self):
        """Test SQL injection in authentication."""
        malicious_inputs = [
            "<EMAIL>'; DROP TABLE users; --",
            "<EMAIL>' OR '1'='1",
            "<EMAIL>' UNION SELECT * FROM users --",
            "'; DELETE FROM users WHERE '1'='1"
        ]
        
        for malicious_email in malicious_inputs:
            response = self.client.post("/auth/login", json={
                "email": malicious_email,
                "password": "password"
            })
            
            # Should get validation error or 401, not 500
            if response.status_code in [401, 422]:
                print("✓ SQL injection attempt blocked")
            elif response.status_code == 500:
                print(f"⚠ Potential SQL injection vulnerability: {malicious_email}")
    
    @task(1)
    def test_password_brute_force(self):
        """Test password brute force protection."""
        common_passwords = [
            "password", "123456", "admin", "password123",
            "qwerty", "letmein", "welcome", "monkey"
        ]
        
        for password in common_passwords:
            response = self.client.post("/auth/login", json={
                "email": "<EMAIL>",
                "password": password
            })
            
            if response.status_code == 429:
                print("✓ Brute force protection active")
                break
            elif response.status_code == 423:
                print("✓ Account locked due to failed attempts")
                break

# Custom load test scenarios
class PeakLoadUser(HttpUser):
    """Simulate peak load conditions."""
    
    wait_time = between(0.1, 1)
    weight = 3  # Higher weight for peak load simulation
    
    def on_start(self):
        """Setup for peak load testing."""
        self.user_batch = random.randint(1, 1000)
        self.email = f"peak_{self.user_batch}@example.com"
        self.password = "PeakLoad123!"
    
    @task(10)
    def simulate_active_user(self):
        """Simulate an active user session."""
        # Login
        login_response = self.client.post("/auth/login", json={
            "email": self.email,
            "password": self.password
        })
        
        if login_response.status_code == 200:
            token = login_response.json().get("access_token")
            headers = {"Authorization": f"Bearer {token}"}
            
            # Simulate user activity
            activities = [
                lambda: self.client.get("/auth/me", headers=headers),
                lambda: self.client.get("/auth/api-keys", headers=headers),
                lambda: self.client.get("/auth/health"),
            ]
            
            # Perform random activities
            for _ in range(random.randint(1, 5)):
                activity = random.choice(activities)
                activity()

# Usage instructions:
"""
Run load tests with different scenarios:

1. Basic load test:
   locust -f load_test_auth.py --host=http://localhost:8000

2. High volume test:
   locust -f load_test_auth.py --host=http://localhost:8000 -u 100 -r 10 -t 300s

3. Security test:
   locust -f load_test_auth.py --host=http://localhost:8000 -u 50 -r 5 --only-summary

4. Peak load simulation:
   locust -f load_test_auth.py --host=http://localhost:8000 -u 200 -r 20 -t 600s

5. Specific user class:
   locust -f load_test_auth.py SecurityTestUser --host=http://localhost:8000

Environment variables for testing:
- LOAD_TEST_USERS: Number of concurrent users (default: 10)
- LOAD_TEST_DURATION: Test duration in seconds (default: 300)
- LOAD_TEST_SPAWN_RATE: User spawn rate per second (default: 1)
"""
