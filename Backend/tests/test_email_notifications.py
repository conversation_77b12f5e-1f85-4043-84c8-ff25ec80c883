import pytest
from unittest.mock import AsyncMock, patch

from app.core.auth_service import AuthService
from app.core.otp_service import OTPService
from app.schemas.auth import UserLogin, OTPVerification
from app.core.email_service import EmailService

@pytest.fixture
def mock_db_pool():
    """Provides a mock database pool."""
    return AsyncMock()

@pytest.fixture
def mock_email_service():
    """Provides a mock EmailService."""
    return AsyncMock(spec=EmailService)

@pytest.fixture
def otp_service(mock_db_pool, mock_email_service):
    """Provides an OTPService instance with a mock email service."""
    return OTPService(db_pool=mock_db_pool, email_service=mock_email_service)

@pytest.fixture
def auth_service(mock_db_pool, otp_service, mock_email_service):
    """Provides an AuthService instance with mock dependencies."""
    return AuthService(db_pool=mock_db_pool, otp_service=otp_service, email_service=mock_email_service)

@pytest.mark.asyncio
async def test_welcome_email_sent_on_verify(otp_service, mock_email_service):
    """Test that a welcome email is sent upon successful OTP verification."""
    # Arrange
    email = "<EMAIL>"
    otp_code = "123456"
    user_id = "test-user-id-welcome"
    first_name = "Welcome"

    mock_otp_record = {
        "id": "otp-record-id",
        "user_id": user_id,
        "attempts": 0,
        "max_attempts": 3,
        "is_used": False
    }
    mock_user_record = {"email": email, "first_name": first_name}

    with patch.object(otp_service.db_pool, 'acquire') as mock_acquire:
        mock_conn = AsyncMock()
        mock_acquire.return_value.__aenter__.return_value = mock_conn
        # Mock finding a valid OTP record
        mock_conn.fetchrow.side_effect = [mock_otp_record, mock_user_record]

        # Act
        await otp_service.verify_email_otp(email, otp_code)

        # Assert
        # Check that user is verified and activated
        mock_conn.execute.assert_any_call("UPDATE users SET is_verified = true, is_active = true WHERE id = $1", user_id)
        # Check that the welcome email was sent with the correct details
        mock_email_service.send_welcome_email.assert_called_once_with(email, first_name)

@pytest.mark.asyncio
async def test_account_locked_email_sent(auth_service, mock_email_service):
    """Test that an account locked email is sent after 5 failed login attempts."""
    # Arrange
    login_data = UserLogin(email="<EMAIL>", password="wrong-password")
    user_id = "test-user-id-locked"
    first_name = "Locked"

    # Mock user data for a user who is about to be locked
    mock_user = {
        'id': user_id,
        'email': login_data.email,
        'password_hash': '$2b$12$abcdefghijklmnopqrstuv.w.xyz.ABCDEFGHIJKL', # Dummy hash
        'is_active': True,
        'is_verified': True,
        'first_name': first_name,
        'subscription_tier': 'free',
        'failed_login_attempts': 4, # 4 previous failed attempts
        'locked_until': None
    }

    with patch.object(auth_service.db_pool, 'acquire') as mock_acquire, \
         patch('bcrypt.checkpw', return_value=False): # Mock password check to fail
        mock_conn = AsyncMock()
        mock_acquire.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetchrow.return_value = mock_user

        # Act
        try:
            await auth_service.login_user(login_data)
        except Exception:
            pass # Expecting an HTTPException for unauthorized

        # Assert
        # Check that the account lock email was sent
        mock_email_service.send_account_locked_email.assert_called_once_with(login_data.email, first_name)
        # Check that failed attempts and locked_until are updated
        update_query = [call for call in mock_conn.execute.call_args_list if 'UPDATE users' in call.args[0]][0]
        assert 'failed_login_attempts = $1' in update_query.args[0]
        assert update_query.args[1] == 5 # 4 + 1
        assert update_query.args[2] is not None # locked_until is set
