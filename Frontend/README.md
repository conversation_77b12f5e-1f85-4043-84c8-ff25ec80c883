# ConTXT Frontend

## 🚀 Quick Start

ConTXT is an AI-powered context engineering platform with a modern, interactive frontend built with Next.js 15, React 19, and TypeScript.

### Prerequisites
- Node.js 18+
- pnpm (recommended)

### Installation & Development
```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev

# Build for production
pnpm build
```

### Project Structure
```
Frontend/
├── docs/           # 📚 Comprehensive documentation
├── src/            # 🔧 Source code
├── public/         # 📁 Static assets
├── config/         # ⚙️ Configuration files
└── package.json    # 📦 Dependencies
```

## 🏗️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript 5
- **UI**: React 19 + Radix UI + Tailwind CSS
- **Theme**: Dark/light mode with OKLCH colors
- **Icons**: Lucide React
- **Animations**: Canvas-based interactive mesh background

## 📚 Documentation

For comprehensive documentation, see the [`docs/`](./docs/) directory:

- **[📖 Complete Documentation Index](./docs/README.md)** - Start here
- **[🧩 Components Guide](./docs/components.md)** - Component architecture
- **[🎨 Theme System](./docs/theme.md)** - Design system and theming
- **[⚡ Performance Guide](./docs/performance.md)** - Optimization strategies
- **[🔐 Security Analysis](./docs/security.md)** - Security assessment
- **[🛠️ Development Guide](./docs/development.md)** - Development workflow
- **[🔗 API Integration](./docs/api-integration.md)** - Backend integration
- **[📊 Current State](./docs/current-state.md)** - Implementation analysis

## ✨ Key Features

### Interactive Mesh Background
- Canvas-based particle system with 60fps performance
- Real-time mouse interactions and gravity effects
- Device-responsive configurations (120/80/60 nodes)
- Spatial optimization with viewport culling

### Component Architecture
- **Radix UI** primitives for accessibility
- **Feature-based** organization for scalability
- **TypeScript** for type safety
- **Clean imports** with barrel exports

### Design System
- **OKLCH color space** for perceptual uniformity
- **Dark/light themes** with system preference detection
- **Responsive typography** with Inter font
- **Consistent spacing** and border radius system

## 🛠️ Development

### Available Scripts
```bash
pnpm dev          # Start development server
pnpm build        # Build for production
pnpm start        # Start production server
pnpm lint         # Run ESLint
pnpm type-check   # Run TypeScript checks
```

### Project Organization
- **`src/app/`** - Next.js App Router pages
- **`src/components/`** - React components organized by feature
- **`src/hooks/`** - Custom React hooks
- **`src/lib/`** - Utility functions and configurations
- **`docs/`** - Comprehensive documentation
- **`config/`** - Configuration files

### Import Paths
```typescript
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks"
import { cn } from "@/lib/utils"
import { ConTXTLanding } from "@/components/features/landing"
```

## 🚀 Performance

- **Lighthouse Score**: Target >90 across all metrics
- **Bundle Size**: <1MB initial load
- **Interactive Mesh**: 60fps with spatial optimization
- **Core Web Vitals**: LCP <2.5s, FID <100ms, CLS <0.1

## 🔐 Security

Current security score: **6.5/10**
- ✅ No dangerous React patterns
- ✅ Secure component architecture
- ⚠️ Needs production configuration hardening
- ⚠️ Requires input validation implementation

---

*For detailed information, see the [complete documentation](./docs/README.md).*
