# ConTXT Frontend Documentation

## 📚 Documentation Index

Welcome to the ConTXT frontend documentation. This directory contains comprehensive guides for understanding, developing, and maintaining the ConTXT frontend application.

### 🚀 Quick Start
- [Main README](../README.md) - Project overview and setup
- [Development Guide](./development.md) - Development workflow and guidelines

### 📖 Core Documentation

#### Architecture & Design
- **[Components Guide](./components.md)** - Component architecture and usage patterns
- **[Theme System](./theme.md)** - Design system, colors, typography, and theming
- **[Current State Analysis](./current-state.md)** - Detailed analysis of current implementation

#### Development
- **[Development Guide](./development.md)** - Setup, workflow, and best practices
- **[API Integration](./api-integration.md)** - Backend integration patterns and examples
- **[Performance Guide](./performance.md)** - Optimization strategies and monitoring

#### Security & Quality
- **[Security Analysis](./security.md)** - Security assessment and recommendations

## 🏗️ Project Structure

The ConTXT frontend follows a modern, scalable architecture:

```
Frontend/
├── docs/                           # 📚 Documentation
├── src/                            # 🔧 Source code
│   ├── app/                        # Next.js App Router
│   ├── components/                 # React components
│   │   ├── ui/                     # Base UI components
│   │   ├── features/               # Feature-specific components
│   │   ├── layout/                 # Layout components
│   │   └── providers/              # Context providers
│   ├── hooks/                      # Custom React hooks
│   ├── lib/                        # Utility libraries
│   ├── styles/                     # Global styles
│   └── types/                      # TypeScript definitions
├── public/                         # Static assets
├── config/                         # Configuration files
└── package.json                    # Dependencies and scripts
```

## 🎯 Key Features

### Interactive Mesh Background
- Canvas-based particle system
- Real-time mouse interactions
- Performance-optimized rendering
- Device-responsive configurations

### Component Architecture
- **Radix UI** primitives for accessibility
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **Feature-based** organization

### Design System
- **OKLCH color space** for perceptual uniformity
- **Dark/light theme** support
- **Responsive typography** scale
- **Consistent spacing** system

## 🛠️ Development Workflow

### Getting Started
```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev

# Build for production
pnpm build
```

### Code Organization
- **Feature-based** component structure
- **Clean imports** with barrel exports
- **TypeScript paths** for easy navigation
- **Consistent naming** conventions

### Testing & Quality
- **Type checking** with TypeScript
- **Linting** with ESLint
- **Performance monitoring** with Core Web Vitals
- **Security analysis** with automated tools

## 📊 Performance Metrics

### Current Targets
- **Lighthouse Score**: > 90 across all metrics
- **Bundle Size**: < 1MB initial load
- **Time to Interactive**: < 3 seconds
- **Core Web Vitals**: All metrics in green

### Optimization Strategies
- **Code splitting** at route and component level
- **Image optimization** with Next.js Image
- **Bundle analysis** for size monitoring
- **Performance profiling** for bottlenecks

## 🔐 Security Considerations

### Current Security Score: 6.5/10

### Key Areas
- **Input validation** with Zod schemas
- **XSS prevention** with React defaults
- **CSRF protection** with security headers
- **Dependency management** with version pinning

### Immediate Actions
- Remove development flags from production
- Implement comprehensive security headers
- Add input validation to all forms
- Pin dependency versions

## 🎨 Design System

### Color Palette
- **Primary**: Red gradient system (`#ef4444` to `#dc2626`)
- **Background**: OKLCH-based dark/light themes
- **Semantic**: Success, warning, error states
- **Charts**: Data visualization colors

### Typography
- **Font**: Inter from Google Fonts
- **Scale**: Responsive from 12px to 128px
- **Weights**: Regular, medium, semibold, bold
- **Colors**: Theme-aware text colors

### Components
- **Button**: 6 variants, 4 sizes
- **Card**: Composable layout component
- **Form**: Accessible input components
- **Navigation**: Responsive menu systems

## 🔄 Recent Changes

### Frontend Reorganization (Latest)
- **New structure**: Feature-based organization
- **Clean imports**: Barrel exports for all modules
- **Updated paths**: TypeScript path mapping
- **Documentation**: Centralized in `/docs` directory

### Benefits
- **Better maintainability** with clear separation
- **Improved developer experience** with intuitive structure
- **Scalable architecture** for future growth
- **Enhanced documentation** for team collaboration

## 📞 Support & Contributing

### Getting Help
- Check the relevant documentation section
- Review the [Development Guide](./development.md)
- Examine the [Current State Analysis](./current-state.md)

### Contributing
- Follow the [Development Guide](./development.md) workflow
- Maintain component patterns from [Components Guide](./components.md)
- Ensure security compliance per [Security Analysis](./security.md)
- Test performance impact using [Performance Guide](./performance.md)

---

*This documentation is maintained alongside the codebase and reflects the current state of the ConTXT frontend application.*
