# ConTXT Frontend API Integration Guide

## 🔗 Overview

This guide outlines the integration patterns between the ConTXT frontend and backend services, including authentication, data fetching, real-time updates, and error handling strategies.

## 🏗️ API Architecture

### Backend Services
- **Main API**: FastAPI backend at `/api/v1/`
- **Authentication**: JWT-based authentication
- **WebSocket**: Real-time updates for live features
- **File Upload**: Multipart form data handling
- **Search**: Elasticsearch integration for context search

### Frontend Integration Points
```typescript
// API configuration
const API_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
  version: 'v1',
  timeout: 30000,
  retries: 3
}

// API endpoints
const ENDPOINTS = {
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    refresh: '/auth/refresh',
    logout: '/auth/logout'
  },
  context: {
    create: '/context',
    list: '/context',
    get: '/context/:id',
    update: '/context/:id',
    delete: '/context/:id'
  },
  search: {
    query: '/search',
    suggestions: '/search/suggestions'
  },
  files: {
    upload: '/files/upload',
    process: '/files/process/:id'
  }
}
```

## 🔐 Authentication System

### JWT Token Management
```typescript
// lib/auth.ts
interface AuthTokens {
  accessToken: string
  refreshToken: string
  expiresAt: number
}

class AuthManager {
  private tokens: AuthTokens | null = null

  setTokens(tokens: AuthTokens) {
    this.tokens = tokens
    localStorage.setItem('auth_tokens', JSON.stringify(tokens))
  }

  getAccessToken(): string | null {
    if (!this.tokens) {
      const stored = localStorage.getItem('auth_tokens')
      if (stored) {
        this.tokens = JSON.parse(stored)
      }
    }

    if (this.tokens && this.tokens.expiresAt > Date.now()) {
      return this.tokens.accessToken
    }

    return null
  }

  async refreshTokens(): Promise<string | null> {
    if (!this.tokens?.refreshToken) return null

    try {
      const response = await fetch(`${API_CONFIG.baseURL}/auth/refresh`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken: this.tokens.refreshToken })
      })

      if (response.ok) {
        const newTokens = await response.json()
        this.setTokens(newTokens)
        return newTokens.accessToken
      }
    } catch (error) {
      console.error('Token refresh failed:', error)
    }

    this.clearTokens()
    return null
  }

  clearTokens() {
    this.tokens = null
    localStorage.removeItem('auth_tokens')
  }
}

export const authManager = new AuthManager()
```

### Authentication Hook
```typescript
// hooks/use-auth.ts
import { useState, useEffect, useContext, createContext } from 'react'

interface User {
  id: string
  email: string
  name: string
  role: 'user' | 'premium' | 'team' | 'admin'
}

interface AuthContextType {
  user: User | null
  login: (email: string, password: string) => Promise<void>
  logout: () => void
  loading: boolean
  error: string | null
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Check for existing session on mount
    checkAuthStatus()
  }, [])

  const checkAuthStatus = async () => {
    const token = authManager.getAccessToken()
    if (token) {
      try {
        const response = await apiClient.get('/auth/me')
        setUser(response.data)
      } catch (error) {
        authManager.clearTokens()
      }
    }
    setLoading(false)
  }

  const login = async (email: string, password: string) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`${API_CONFIG.baseURL}/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password })
      })

      if (response.ok) {
        const { user, tokens } = await response.json()
        authManager.setTokens(tokens)
        setUser(user)
      } else {
        const errorData = await response.json()
        setError(errorData.message || 'Login failed')
      }
    } catch (error) {
      setError('Network error occurred')
    } finally {
      setLoading(false)
    }
  }

  const logout = () => {
    authManager.clearTokens()
    setUser(null)
  }

  return (
    <AuthContext.Provider value={{ user, login, logout, loading, error }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
```

## 🌐 API Client Setup

### HTTP Client Configuration
```typescript
// lib/api-client.ts
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

class APIClient {
  private client: AxiosInstance

  constructor() {
    this.client = axios.create({
      baseURL: `${API_CONFIG.baseURL}/api/${API_CONFIG.version}`,
      timeout: API_CONFIG.timeout,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // Request interceptor for auth tokens
    this.client.interceptors.request.use(
      (config) => {
        const token = authManager.getAccessToken()
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => Promise.reject(error)
    )

    // Response interceptor for token refresh
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true

          const newToken = await authManager.refreshTokens()
          if (newToken) {
            originalRequest.headers.Authorization = `Bearer ${newToken}`
            return this.client(originalRequest)
          }
        }

        return Promise.reject(error)
      }
    )
  }

  // HTTP methods
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get<T>(url, config)
    return response.data
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.post<T>(url, data, config)
    return response.data
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.put<T>(url, data, config)
    return response.data
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete<T>(url, config)
    return response.data
  }

  // File upload with progress
  async uploadFile(
    url: string,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<any> {
    const formData = new FormData()
    formData.append('file', file)

    return this.client.post(url, formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = (progressEvent.loaded / progressEvent.total) * 100
          onProgress(progress)
        }
      }
    })
  }
}

export const apiClient = new APIClient()
```

### Data Fetching Hooks
```typescript
// hooks/use-api.ts
import { useState, useEffect } from 'react'
import useSWR from 'swr'

// Generic data fetching hook
export function useAPI<T>(
  endpoint: string,
  options?: {
    enabled?: boolean
    refreshInterval?: number
    onError?: (error: any) => void
  }
) {
  const { data, error, mutate, isLoading } = useSWR<T>(
    options?.enabled !== false ? endpoint : null,
    (url: string) => apiClient.get<T>(url),
    {
      refreshInterval: options?.refreshInterval,
      onError: options?.onError,
      revalidateOnFocus: false
    }
  )

  return {
    data,
    error,
    loading: isLoading,
    refetch: mutate
  }
}

// Mutation hook for POST/PUT/DELETE operations
export function useMutation<TData, TVariables>(
  mutationFn: (variables: TVariables) => Promise<TData>
) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const mutate = async (variables: TVariables): Promise<TData | null> => {
    setLoading(true)
    setError(null)

    try {
      const result = await mutationFn(variables)
      return result
    } catch (err: any) {
      setError(err.message || 'An error occurred')
      return null
    } finally {
      setLoading(false)
    }
  }

  return { mutate, loading, error }
}
```

## 🔍 Context Search Integration

### Search API Integration
```typescript
// hooks/use-search.ts
interface SearchQuery {
  query: string
  filters?: {
    type?: string[]
    dateRange?: [Date, Date]
    tags?: string[]
  }
  pagination?: {
    page: number
    limit: number
  }
}

interface SearchResult {
  id: string
  title: string
  content: string
  type: string
  relevanceScore: number
  metadata: Record<string, any>
  createdAt: string
}

interface SearchResponse {
  results: SearchResult[]
  total: number
  page: number
  totalPages: number
  suggestions?: string[]
}

export function useSearch() {
  const [query, setQuery] = useState<SearchQuery>({ query: '' })
  const [results, setResults] = useState<SearchResponse | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const search = useMutation<SearchResponse, SearchQuery>(
    async (searchQuery) => {
      return apiClient.post('/search', searchQuery)
    }
  )

  const performSearch = async (newQuery: SearchQuery) => {
    setQuery(newQuery)
    const result = await search.mutate(newQuery)
    if (result) {
      setResults(result)
    }
  }

  const getSuggestions = async (partialQuery: string): Promise<string[]> => {
    try {
      const response = await apiClient.get(`/search/suggestions?q=${encodeURIComponent(partialQuery)}`)
      return response.suggestions || []
    } catch (error) {
      return []
    }
  }

  return {
    query,
    results,
    loading: search.loading,
    error: search.error,
    performSearch,
    getSuggestions
  }
}
```

### Search Component Integration
```typescript
// components/search/SearchInterface.tsx
import { useState, useEffect } from 'react'
import { useDebounce } from 'use-debounce'
import { useSearch } from '@/hooks/use-search'

export function SearchInterface() {
  const [inputValue, setInputValue] = useState('')
  const [debouncedQuery] = useDebounce(inputValue, 300)
  const [suggestions, setSuggestions] = useState<string[]>([])

  const { results, loading, error, performSearch, getSuggestions } = useSearch()

  // Get suggestions as user types
  useEffect(() => {
    if (debouncedQuery.length > 2) {
      getSuggestions(debouncedQuery).then(setSuggestions)
    } else {
      setSuggestions([])
    }
  }, [debouncedQuery, getSuggestions])

  // Perform search when user submits
  const handleSearch = (searchQuery: string) => {
    performSearch({
      query: searchQuery,
      pagination: { page: 1, limit: 20 }
    })
  }

  return (
    <div className="search-interface">
      <div className="relative">
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={(e) => e.key === 'Enter' && handleSearch(inputValue)}
          placeholder="Search your context..."
          className="w-full p-3 border rounded-lg"
        />
        
        {suggestions.length > 0 && (
          <div className="absolute top-full left-0 right-0 bg-white border rounded-lg shadow-lg z-10">
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => {
                  setInputValue(suggestion)
                  handleSearch(suggestion)
                }}
                className="w-full text-left p-2 hover:bg-gray-100"
              >
                {suggestion}
              </button>
            ))}
          </div>
        )}
      </div>

      {loading && <div>Searching...</div>}
      {error && <div className="text-red-500">Error: {error}</div>}
      
      {results && (
        <div className="search-results mt-4">
          <div className="text-sm text-gray-600 mb-4">
            Found {results.total} results
          </div>
          
          {results.results.map((result) => (
            <div key={result.id} className="border rounded-lg p-4 mb-4">
              <h3 className="font-semibold">{result.title}</h3>
              <p className="text-gray-600 mt-1">{result.content}</p>
              <div className="text-xs text-gray-500 mt-2">
                Relevance: {(result.relevanceScore * 100).toFixed(1)}%
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
```

## 🔄 Real-time Updates

### WebSocket Integration
```typescript
// hooks/use-websocket.ts
import { useEffect, useRef, useState } from 'react'

interface WebSocketMessage {
  type: string
  payload: any
  timestamp: string
}

export function useWebSocket(url: string) {
  const [isConnected, setIsConnected] = useState(false)
  const [messages, setMessages] = useState<WebSocketMessage[]>([])
  const [error, setError] = useState<string | null>(null)
  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>()

  const connect = () => {
    try {
      const token = authManager.getAccessToken()
      const wsUrl = `${url}?token=${token}`
      
      wsRef.current = new WebSocket(wsUrl)

      wsRef.current.onopen = () => {
        setIsConnected(true)
        setError(null)
        console.log('WebSocket connected')
      }

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data)
          setMessages(prev => [...prev, message])
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err)
        }
      }

      wsRef.current.onclose = () => {
        setIsConnected(false)
        console.log('WebSocket disconnected')
        
        // Attempt to reconnect after 3 seconds
        reconnectTimeoutRef.current = setTimeout(() => {
          connect()
        }, 3000)
      }

      wsRef.current.onerror = (error) => {
        setError('WebSocket connection error')
        console.error('WebSocket error:', error)
      }
    } catch (err) {
      setError('Failed to establish WebSocket connection')
    }
  }

  const sendMessage = (message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message))
    }
  }

  const disconnect = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }
    wsRef.current?.close()
  }

  useEffect(() => {
    connect()
    return disconnect
  }, [url])

  return {
    isConnected,
    messages,
    error,
    sendMessage,
    disconnect
  }
}
```

## 📁 File Upload Integration

### File Upload Hook
```typescript
// hooks/use-file-upload.ts
interface UploadProgress {
  loaded: number
  total: number
  percentage: number
}

interface UploadResult {
  id: string
  filename: string
  size: number
  url: string
  processingStatus: 'pending' | 'processing' | 'completed' | 'failed'
}

export function useFileUpload() {
  const [uploads, setUploads] = useState<Map<string, UploadResult>>(new Map())
  const [progress, setProgress] = useState<Map<string, UploadProgress>>(new Map())

  const uploadFile = async (file: File): Promise<string> => {
    const uploadId = `upload_${Date.now()}_${Math.random()}`
    
    // Initialize upload state
    setUploads(prev => new Map(prev.set(uploadId, {
      id: uploadId,
      filename: file.name,
      size: file.size,
      url: '',
      processingStatus: 'pending'
    })))

    try {
      const result = await apiClient.uploadFile(
        '/files/upload',
        file,
        (progressValue) => {
          setProgress(prev => new Map(prev.set(uploadId, {
            loaded: progressValue,
            total: 100,
            percentage: progressValue
          })))
        }
      )

      // Update upload result
      setUploads(prev => new Map(prev.set(uploadId, {
        ...prev.get(uploadId)!,
        url: result.url,
        processingStatus: 'processing'
      })))

      // Poll for processing completion
      pollProcessingStatus(uploadId, result.id)

      return uploadId
    } catch (error) {
      setUploads(prev => new Map(prev.set(uploadId, {
        ...prev.get(uploadId)!,
        processingStatus: 'failed'
      })))
      throw error
    }
  }

  const pollProcessingStatus = async (uploadId: string, fileId: string) => {
    const poll = async () => {
      try {
        const status = await apiClient.get(`/files/process/${fileId}`)
        
        setUploads(prev => new Map(prev.set(uploadId, {
          ...prev.get(uploadId)!,
          processingStatus: status.status
        })))

        if (status.status === 'processing') {
          setTimeout(poll, 2000) // Poll every 2 seconds
        }
      } catch (error) {
        console.error('Failed to poll processing status:', error)
      }
    }

    poll()
  }

  return {
    uploads: Array.from(uploads.values()),
    progress: Array.from(progress.values()),
    uploadFile
  }
}
```

## 🚨 Error Handling

### Global Error Handler
```typescript
// lib/error-handler.ts
export class APIError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string
  ) {
    super(message)
    this.name = 'APIError'
  }
}

export function handleAPIError(error: any): APIError {
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response
    return new APIError(
      data.message || 'An error occurred',
      status,
      data.code
    )
  } else if (error.request) {
    // Network error
    return new APIError('Network error occurred', 0, 'NETWORK_ERROR')
  } else {
    // Other error
    return new APIError(error.message || 'Unknown error', 0, 'UNKNOWN_ERROR')
  }
}

// Error boundary for API errors
export function APIErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      fallback={({ error }) => (
        <div className="error-container">
          <h2>Something went wrong</h2>
          <p>{error.message}</p>
          <button onClick={() => window.location.reload()}>
            Reload Page
          </button>
        </div>
      )}
    >
      {children}
    </ErrorBoundary>
  )
}
```

---

*This API integration guide provides the foundation for connecting the ConTXT frontend with backend services, ensuring robust, scalable, and maintainable data flow.*
