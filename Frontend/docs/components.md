# ConTXT Frontend Components Documentation

## 🧩 Component Architecture

The ConTXT frontend follows a modular component architecture with clear separation of concerns, reusable UI primitives, and consistent design patterns.

## 📁 Component Organization

### Directory Structure
```
Frontend/
├── components/
│   ├── ui/                 # Base UI components (Radix-based)
│   └── theme-provider.tsx  # Theme context provider
├── ui/                     # Additional UI components
├── hooks/                  # Custom React hooks
└── *.tsx                   # Feature components
```

## 🎨 UI Component Library (`ui/`)

### Base Components

#### Button (`ui/button.tsx`)
**Purpose**: Primary interactive element with multiple variants

<augment_code_snippet path="Frontend/ui/button.tsx" mode="EXCERPT">
````typescript
const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
        destructive: "bg-destructive text-white shadow-xs hover:bg-destructive/90",
        outline: "border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-9 px-4 py-2 has-[>svg]:px-3",
        sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
        lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
        icon: "size-9",
      },
    },
  }
)
````
</augment_code_snippet>

**Features:**
- Multiple variants (default, destructive, outline, secondary, ghost, link)
- Size variations (sm, default, lg, icon)
- Icon support with automatic spacing
- Accessibility features (focus states, disabled states)
- TypeScript support with proper prop types

#### Card (`ui/card.tsx`)
**Purpose**: Container component for grouped content

**Sub-components:**
- `Card`: Main container
- `CardHeader`: Header section with padding
- `CardTitle`: Styled title component
- `CardDescription`: Subtitle/description text
- `CardContent`: Main content area
- `CardFooter`: Footer section for actions

#### Dialog (`ui/dialog.tsx`)
**Purpose**: Modal dialogs and overlays

**Features:**
- Radix UI Dialog primitive
- Overlay with backdrop blur
- Responsive positioning
- Keyboard navigation support
- Focus management

#### Form Components
- `Input`: Text input with validation states
- `Label`: Accessible form labels
- `Select`: Dropdown selection component
- `Checkbox`: Boolean input component
- `Switch`: Toggle component
- `Textarea`: Multi-line text input

#### Navigation Components
- `Tabs`: Tabbed interface component
- `Breadcrumb`: Navigation breadcrumb
- `Navigation Menu`: Complex navigation structures
- `Dropdown Menu`: Context menus and dropdowns

#### Feedback Components
- `Toast`: Notification system
- `Alert`: Inline alert messages
- `Progress`: Progress indicators
- `Skeleton`: Loading placeholders

### Component Patterns

#### Composition Pattern
```typescript
// Card composition example
<Card>
  <CardHeader>
    <CardTitle>Feature Title</CardTitle>
    <CardDescription>Feature description</CardDescription>
  </CardHeader>
  <CardContent>
    {/* Main content */}
  </CardContent>
  <CardFooter>
    <Button>Action</Button>
  </CardFooter>
</Card>
```

#### Variant Pattern
```typescript
// Button variants
<Button variant="default">Primary Action</Button>
<Button variant="outline">Secondary Action</Button>
<Button variant="ghost">Subtle Action</Button>
```

#### Forwarded Refs Pattern
```typescript
const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    // Component implementation
  }
)
```

## 🎯 Feature Components

### ConTXT Landing (`contxt-landing.tsx`)
**Purpose**: Main marketing and product showcase page

**Structure:**
```typescript
export default function ConTXTLanding() {
  // State management
  const [email, setEmail] = useState("")
  const [isAnnual, setIsAnnual] = useState(false)

  // Feature data
  const features = [
    { icon: Brain, title: "AI Context Engineering", ... },
    { icon: Database, title: "Knowledge Graph Intelligence", ... },
    { icon: Code, title: "Developer-First Integration", ... },
  ]

  // Component sections
  return (
    <div className="relative min-h-screen overflow-hidden bg-black">
      {/* Interactive Background */}
      <InteractiveMeshBackground />
      
      {/* Navigation */}
      <nav>...</nav>
      
      {/* Hero Section */}
      <section>...</section>
      
      {/* Features Section */}
      <section>...</section>
      
      {/* Pricing Section */}
      <section>...</section>
    </div>
  )
}
```

**Key Features:**
- Interactive mesh background integration
- Responsive hero section with animated elements
- Feature showcase with icons and metrics
- Integrated pricing display
- Email capture functionality
- Mobile-optimized navigation

### Interactive Mesh Background (`interactive-mesh-background.tsx`)
**Purpose**: Canvas-based particle visualization system

**Technical Implementation:**
```typescript
interface MeshNode {
  id: string
  x: number
  y: number
  originalX: number
  originalY: number
  vx: number
  vy: number
  radius: number
  connections: string[]
  glowIntensity: number
  pulsePhase: number
  gridX: number
  gridY: number
  isVisible: boolean
}

interface Particle {
  x: number
  y: number
  speed: number
  connection: string
  progress: number
  size: number
  opacity: number
  id: string
  isActive: boolean
}
```

**Performance Optimizations:**
- Spatial grid partitioning for efficient collision detection
- RequestAnimationFrame for smooth animations
- Memory pooling for particles
- Viewport culling for off-screen elements
- Debounced resize handling

**Interactive Features:**
- Mouse proximity effects
- Dynamic particle spawning
- Connection strength visualization
- Responsive particle behavior
- Depth-based visual effects

### Pricing Page (`pricing-page.tsx`)
**Purpose**: Subscription tiers and feature comparison

**Data Structure:**
```typescript
interface PricingPlan {
  id: string
  name: string
  tagline: string
  monthlyPrice: number
  annualPrice: number
  popular?: boolean
  features: {
    aiCreditsIncluded: boolean
    byokRequired: boolean
    maxDailyChats: number
    maxFileSize: number
    historyRetention: number
    supportLevel: string
    teamFeatures?: boolean
  }
  quotas: {
    chatsPerDay: number
    filesPerDay: number
    storageGB: number
    tokensPerDay: number
  }
  coreFeatures: Array<{
    name: string
    included: boolean
    tooltip?: string
  }>
  security: Array<{
    feature: string
    included: boolean | string
  }>
}
```

**Features:**
- Multiple pricing tiers (Free, Premium, Team, Enterprise)
- Annual/monthly pricing toggle
- Feature comparison matrix
- Tooltip explanations for complex features
- Security compliance indicators
- Responsive pricing cards

## 🎨 Theme System

### Theme Provider (`theme-provider.tsx`)
**Purpose**: Global theme management with Next Themes

<augment_code_snippet path="Frontend/theme-provider.tsx" mode="EXCERPT">
````typescript
'use client'

import * as React from 'react'
import {
  ThemeProvider as NextThemesProvider,
  type ThemeProviderProps,
} from 'next-themes'

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return <NextThemesProvider {...props}>{children}</NextThemesProvider>
}
````
</augment_code_snippet>

**Configuration:**
- Default theme: dark
- System preference detection
- Smooth transitions disabled for performance
- Class-based theme switching

### CSS Variables System
**Light Theme:**
```css
:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  /* ... additional variables */
}
```

**Dark Theme:**
```css
.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  /* ... additional variables */
}
```

## 🔧 Custom Hooks

### useToast (`hooks/use-toast.ts`)
**Purpose**: Global toast notification system

**Features:**
- Reducer-based state management
- Automatic dismissal with timeouts
- Multiple toast support with limits
- Action-based API (add, update, dismiss, remove)
- Memory management for cleanup

**Usage:**
```typescript
const { toast } = useToast()

toast({
  title: "Success",
  description: "Operation completed successfully",
  variant: "default"
})
```

### useMobile (`hooks/use-mobile.ts`)
**Purpose**: Responsive design helper for mobile detection

**Implementation:**
```typescript
export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)

  React.useEffect(() => {
    const mql = window.matchMedia("(max-width: 768px)")
    const onChange = () => setIsMobile(window.innerWidth < 768)
    
    mql.addEventListener("change", onChange)
    setIsMobile(window.innerWidth < 768)
    
    return () => mql.removeEventListener("change", onChange)
  }, [])

  return !!isMobile
}
```

## 🎯 Component Best Practices

### Design Principles
1. **Composition over Inheritance**: Use component composition for flexibility
2. **Single Responsibility**: Each component has one clear purpose
3. **Accessibility First**: Built with screen readers and keyboard navigation in mind
4. **Performance Conscious**: Optimized rendering and memory usage
5. **Type Safety**: Full TypeScript support with proper prop types

### Code Patterns
1. **Forwarded Refs**: For proper ref handling in composite components
2. **Variant Props**: Using `class-variance-authority` for consistent APIs
3. **Compound Components**: Related components that work together
4. **Render Props**: For flexible component composition
5. **Custom Hooks**: For reusable stateful logic

### Styling Approach
1. **Utility-First**: Tailwind CSS for rapid development
2. **Design Tokens**: CSS variables for consistent theming
3. **Responsive Design**: Mobile-first breakpoint system
4. **Dark Mode**: Comprehensive dark theme support
5. **Animation**: Purposeful motion with performance considerations

### Testing Strategy
1. **Component Testing**: Individual component behavior
2. **Integration Testing**: Component interaction testing
3. **Accessibility Testing**: Screen reader and keyboard navigation
4. **Visual Regression**: Consistent visual appearance
5. **Performance Testing**: Rendering performance metrics

---

*This component documentation provides a comprehensive overview of the ConTXT frontend component architecture and implementation patterns.*
