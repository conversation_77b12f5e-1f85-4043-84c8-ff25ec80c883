# ConTXT Frontend Development Guide

## 🚀 Getting Started

### Prerequisites
- **Node.js**: 18.0.0 or higher
- **Package Manager**: pnpm (recommended), npm, or yarn
- **Editor**: VS Code with recommended extensions

### Recommended VS Code Extensions
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json"
  ]
}
```

### Environment Setup
1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ConTXT/Frontend
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Start development server**
   ```bash
   pnpm dev
   ```

4. **Open browser**
   Navigate to `http://localhost:3000`

## 📁 Project Structure Deep Dive

### Core Directories
```
Frontend/
├── app/                    # Next.js App Router (v13+)
│   ├── globals.css        # Global styles and CSS variables
│   ├── layout.tsx         # Root layout component
│   ├── page.tsx           # Home page route
│   └── pricing/           # Pricing page route
│       └── page.tsx
├── components/            # Shared React components
│   ├── ui/               # Base UI component library
│   └── theme-provider.tsx # Theme context provider
├── hooks/                # Custom React hooks
├── lib/                  # Utility functions and configurations
├── public/               # Static assets (images, icons, etc.)
├── ui/                   # Additional UI components
└── *.tsx                 # Feature-specific components
```

### File Naming Conventions
- **Components**: PascalCase (`ConTXTLanding.tsx`)
- **Hooks**: camelCase with 'use' prefix (`useToast.ts`)
- **Utilities**: camelCase (`utils.ts`)
- **Pages**: lowercase (`page.tsx`)
- **Types**: PascalCase with 'Type' suffix (`ButtonType.ts`)

## 🛠️ Development Workflow

### 1. Component Development
```typescript
// 1. Create component file
// components/ui/new-component.tsx

import * as React from "react"
import { cn } from "@/lib/utils"

interface NewComponentProps {
  children: React.ReactNode
  className?: string
  variant?: "default" | "secondary"
}

const NewComponent = React.forwardRef<
  HTMLDivElement,
  NewComponentProps
>(({ children, className, variant = "default", ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        "base-styles",
        variant === "secondary" && "secondary-styles",
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
})

NewComponent.displayName = "NewComponent"

export { NewComponent }
```

### 2. Adding New Pages
```typescript
// app/new-page/page.tsx
import { Metadata } from 'next'
import NewPageComponent from '@/components/new-page-component'

export const metadata: Metadata = {
  title: 'New Page - ConTXT',
  description: 'Description of the new page',
}

export default function NewPage() {
  return <NewPageComponent />
}
```

### 3. Custom Hook Development
```typescript
// hooks/use-custom-hook.ts
import { useState, useEffect } from 'react'

interface UseCustomHookOptions {
  initialValue?: string
  autoUpdate?: boolean
}

export function useCustomHook(options: UseCustomHookOptions = {}) {
  const [value, setValue] = useState(options.initialValue || '')
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (options.autoUpdate) {
      // Auto-update logic
    }
  }, [options.autoUpdate])

  return {
    value,
    setValue,
    loading,
  }
}
```

## 🎨 Styling Guidelines

### Tailwind CSS Best Practices
1. **Use Utility Classes**: Prefer utilities over custom CSS
2. **Responsive Design**: Mobile-first approach
3. **Consistent Spacing**: Use Tailwind's spacing scale
4. **Color System**: Use CSS variables for theming

```typescript
// Good: Using utility classes
<div className="flex items-center justify-between p-4 bg-background border border-border rounded-lg">

// Avoid: Inline styles
<div style={{ display: 'flex', padding: '16px' }}>
```

### CSS Variable Usage
```css
/* Use design tokens */
.custom-component {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  border: 1px solid hsl(var(--border));
}
```

### Component Styling Patterns
```typescript
// Using cn() utility for conditional classes
import { cn } from "@/lib/utils"

const Button = ({ variant, size, className, ...props }) => {
  return (
    <button
      className={cn(
        "base-button-styles",
        variant === "primary" && "primary-styles",
        variant === "secondary" && "secondary-styles",
        size === "sm" && "small-styles",
        size === "lg" && "large-styles",
        className
      )}
      {...props}
    />
  )
}
```

## 🔧 Configuration Files

### Next.js Configuration (`next.config.mjs`)
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true, // Temporary for development
  },
  typescript: {
    ignoreBuildErrors: true, // Temporary for development
  },
  images: {
    unoptimized: true, // For static export compatibility
  },
  experimental: {
    // Enable experimental features as needed
  }
}

export default nextConfig
```

### TypeScript Configuration (`tsconfig.json`)
```json
{
  "compilerOptions": {
    "target": "ES6",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [{ "name": "next" }],
    "paths": {
      "@/*": ["./*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

### Tailwind Configuration
```javascript
// tailwind.config.js
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './ui/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // Custom color extensions
      },
      animation: {
        // Custom animations
      }
    },
  },
  plugins: [],
}
```

## 🧪 Testing Strategy

### Testing Setup
```bash
# Install testing dependencies
pnpm add -D @testing-library/react @testing-library/jest-dom jest jest-environment-jsdom
```

### Component Testing Example
```typescript
// __tests__/components/Button.test.tsx
import { render, screen } from '@testing-library/react'
import { Button } from '@/components/ui/button'

describe('Button Component', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument()
  })

  it('applies variant classes correctly', () => {
    render(<Button variant="secondary">Secondary</Button>)
    const button = screen.getByRole('button')
    expect(button).toHaveClass('secondary-variant-class')
  })
})
```

### Integration Testing
```typescript
// __tests__/pages/landing.test.tsx
import { render, screen } from '@testing-library/react'
import ConTXTLanding from '@/contxt-landing'

describe('Landing Page', () => {
  it('renders hero section', () => {
    render(<ConTXTLanding />)
    expect(screen.getByText(/Transform Your Data Into/i)).toBeInTheDocument()
  })

  it('displays feature cards', () => {
    render(<ConTXTLanding />)
    expect(screen.getByText(/AI Context Engineering/i)).toBeInTheDocument()
  })
})
```

## 🚀 Performance Optimization

### Code Splitting
```typescript
// Dynamic imports for large components
import dynamic from 'next/dynamic'

const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <div>Loading...</div>,
  ssr: false // Disable SSR if needed
})
```

### Image Optimization
```typescript
import Image from 'next/image'

// Optimized image usage
<Image
  src="/hero-image.jpg"
  alt="Hero image"
  width={800}
  height={600}
  priority // For above-the-fold images
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,..."
/>
```

### Bundle Analysis
```bash
# Analyze bundle size
pnpm build
npx @next/bundle-analyzer
```

## 🔍 Debugging

### Development Tools
1. **React Developer Tools**: Browser extension for React debugging
2. **Next.js DevTools**: Built-in development features
3. **Tailwind CSS IntelliSense**: VS Code extension for class suggestions

### Common Debugging Techniques
```typescript
// Console debugging
console.log('Debug info:', { state, props })

// React DevTools debugging
const DebugComponent = () => {
  const debugInfo = { /* debug data */ }
  
  // This will show in React DevTools
  React.useDebugValue(debugInfo)
  
  return <div>Component content</div>
}
```

### Error Handling
```typescript
// Error boundary for graceful error handling
'use client'

import React from 'react'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

class ErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-fallback">
          <h2>Something went wrong.</h2>
          <button onClick={() => this.setState({ hasError: false })}>
            Try again
          </button>
        </div>
      )
    }

    return this.props.children
  }
}
```

## 📦 Build and Deployment

### Build Commands
```bash
# Development build
pnpm dev

# Production build
pnpm build

# Start production server
pnpm start

# Lint code
pnpm lint

# Type check
pnpm type-check
```

### Environment Variables
```bash
# .env.local
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_ENVIRONMENT=development
```

### Deployment Checklist
- [ ] Run type checking: `pnpm type-check`
- [ ] Run linting: `pnpm lint`
- [ ] Test build: `pnpm build`
- [ ] Check bundle size
- [ ] Verify environment variables
- [ ] Test in production mode

## 🔄 Git Workflow

### Branch Naming
- `feature/component-name` - New features
- `fix/issue-description` - Bug fixes
- `refactor/area-name` - Code refactoring
- `docs/section-name` - Documentation updates

### Commit Messages
```
feat: add new pricing component
fix: resolve mobile navigation issue
refactor: optimize interactive mesh performance
docs: update component documentation
style: improve button hover states
```

### Pull Request Process
1. Create feature branch from `main`
2. Implement changes with tests
3. Update documentation if needed
4. Create pull request with description
5. Request code review
6. Address feedback and merge

---

*This development guide provides comprehensive information for contributing to the ConTXT frontend codebase.*
