# ConTXT Frontend Performance Guide

## 🎯 Performance Targets

### Core Web Vitals Goals
- **Largest Contentful Paint (LCP)**: < 2.5 seconds
- **First Input Delay (FID)**: < 100 milliseconds  
- **Cumulative Layout Shift (CLS)**: < 0.1
- **First Contentful Paint (FCP)**: < 1.8 seconds
- **Time to First Byte (TTFB)**: < 600 milliseconds

### Business Performance Metrics
- **Bundle Size**: < 1MB initial load
- **Time to Interactive**: < 3 seconds
- **Lighthouse Score**: > 90 across all categories
- **Memory Usage**: < 100MB on mobile devices

## 🚀 Current Optimizations

### 1. Next.js App Router Benefits
- **Automatic Code Splitting**: Route-based splitting by default
- **Server Components**: Reduced client-side JavaScript
- **Streaming**: Progressive page loading
- **Built-in Optimizations**: Image, font, and script optimization

### 2. Bundle Optimization
```javascript
// next.config.mjs optimizations
const nextConfig = {
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons']
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production'
  }
}
```

### 3. Image Optimization
```typescript
// Optimized image usage throughout the app
import Image from 'next/image'

<Image
  src="/hero-background.jpg"
  alt="ConTXT Hero"
  width={1920}
  height={1080}
  priority={true}
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
/>
```

### 4. Font Optimization
```typescript
// app/layout.tsx - Optimized font loading
import { Inter } from "next/font/google"

const inter = Inter({ 
  subsets: ["latin"],
  display: 'swap',
  preload: true
})
```

## 🎨 Interactive Mesh Background Optimization

### Performance Challenges
The `InteractiveMeshBackground` component is the most performance-critical part of the application due to:
- Canvas rendering with 60fps animations
- Real-time particle system calculations
- Mouse interaction tracking
- Dynamic particle spawning and cleanup

### Optimization Strategies

#### 1. Spatial Partitioning
```typescript
// Efficient collision detection using spatial grid
interface SpatialGrid {
  [key: string]: MeshNode[]
}

const createSpatialGrid = (nodes: MeshNode[], cellSize: number): SpatialGrid => {
  const grid: SpatialGrid = {}
  
  nodes.forEach(node => {
    const gridX = Math.floor(node.x / cellSize)
    const gridY = Math.floor(node.y / cellSize)
    const key = `${gridX},${gridY}`
    
    if (!grid[key]) grid[key] = []
    grid[key].push(node)
  })
  
  return grid
}
```

#### 2. Object Pooling
```typescript
// Particle object pooling to reduce garbage collection
class ParticlePool {
  private pool: Particle[] = []
  private active: Particle[] = []

  getParticle(): Particle {
    return this.pool.pop() || this.createParticle()
  }

  releaseParticle(particle: Particle): void {
    particle.isActive = false
    this.pool.push(particle)
  }

  private createParticle(): Particle {
    return {
      x: 0, y: 0, speed: 0, connection: '',
      progress: 0, size: 0, opacity: 0,
      id: '', isActive: false
    }
  }
}
```

#### 3. Viewport Culling
```typescript
// Only render visible elements
const isInViewport = (node: MeshNode, viewport: Viewport): boolean => {
  return (
    node.x >= viewport.left - 50 &&
    node.x <= viewport.right + 50 &&
    node.y >= viewport.top - 50 &&
    node.y <= viewport.bottom + 50
  )
}
```

#### 4. Optimized Animation Loop
```typescript
// Efficient animation with requestAnimationFrame
const animate = useCallback(() => {
  if (!canvasRef.current || !contextRef.current) return

  const ctx = contextRef.current
  const now = performance.now()
  const deltaTime = now - lastFrameTime.current
  
  // Limit to 60fps
  if (deltaTime < 16.67) {
    animationFrameRef.current = requestAnimationFrame(animate)
    return
  }

  lastFrameTime.current = now

  // Clear canvas efficiently
  ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height)

  // Update and render only visible elements
  updateVisibleNodes(deltaTime)
  renderVisibleNodes(ctx)
  updateParticles(deltaTime)
  renderParticles(ctx)

  animationFrameRef.current = requestAnimationFrame(animate)
}, [])
```

## 📦 Bundle Size Optimization

### Current Bundle Analysis
```bash
# Analyze bundle size
pnpm build
pnpm analyze

# Key metrics to monitor:
# - First Load JS: < 250KB
# - Route JS: < 100KB per route
# - Shared chunks: Optimized splitting
```

### Import Optimization
```typescript
// Good: Tree-shakable imports
import { Button } from "@/components/ui/button"
import { Brain, Code, Database } from "lucide-react"

// Avoid: Barrel imports that include unused code
import * as Icons from "lucide-react" // Imports entire library
```

### Dynamic Imports
```typescript
// Lazy load heavy components
import dynamic from 'next/dynamic'

const InteractiveMeshBackground = dynamic(
  () => import('./interactive-mesh-background'),
  { 
    ssr: false,
    loading: () => <div className="animate-pulse bg-gray-900 h-screen" />
  }
)

const PricingPage = dynamic(
  () => import('./pricing-page'),
  { loading: () => <div>Loading pricing...</div> }
)
```

### Code Splitting Strategies
```typescript
// Route-based splitting (automatic with App Router)
// Component-based splitting for large features
// Library splitting for third-party packages

// webpack.config.js optimization
module.exports = {
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
        }
      }
    }
  }
}
```

## 🎭 Runtime Performance

### React Performance Optimizations

#### 1. Memoization
```typescript
// Memoize expensive calculations
const expensiveValue = useMemo(() => {
  return heavyCalculation(data)
}, [data])

// Memoize callback functions
const handleClick = useCallback((id: string) => {
  onItemClick(id)
}, [onItemClick])

// Memoize components
const MemoizedComponent = memo(({ data }) => {
  return <div>{data.title}</div>
})
```

#### 2. Virtual Scrolling
```typescript
// For large lists, implement virtual scrolling
import { FixedSizeList as List } from 'react-window'

const VirtualizedList = ({ items }) => (
  <List
    height={600}
    itemCount={items.length}
    itemSize={50}
    itemData={items}
  >
    {({ index, style, data }) => (
      <div style={style}>
        {data[index].title}
      </div>
    )}
  </List>
)
```

#### 3. Debounced Interactions
```typescript
// Debounce expensive operations
import { useDebouncedCallback } from 'use-debounce'

const SearchComponent = () => {
  const [query, setQuery] = useState('')
  
  const debouncedSearch = useDebouncedCallback(
    (searchQuery: string) => {
      performSearch(searchQuery)
    },
    300
  )

  useEffect(() => {
    debouncedSearch(query)
  }, [query, debouncedSearch])

  return (
    <input
      value={query}
      onChange={(e) => setQuery(e.target.value)}
      placeholder="Search..."
    />
  )
}
```

### CSS Performance

#### 1. Efficient Selectors
```css
/* Good: Specific, efficient selectors */
.button-primary { }
.card-header { }

/* Avoid: Complex, nested selectors */
.container .content .card .header .title { }
```

#### 2. CSS-in-JS Optimization
```typescript
// Use Tailwind's JIT compiler for optimal CSS
// Purge unused styles in production
// Use CSS variables for dynamic theming

const dynamicStyles = {
  '--primary-color': theme.colors.primary,
  '--secondary-color': theme.colors.secondary,
} as React.CSSProperties
```

## 📊 Performance Monitoring

### Core Web Vitals Tracking
```typescript
// Track performance metrics
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

export function reportWebVitals() {
  getCLS(console.log)
  getFID(console.log)
  getFCP(console.log)
  getLCP(console.log)
  getTTFB(console.log)
}

// In _app.tsx or layout.tsx
useEffect(() => {
  reportWebVitals()
}, [])
```

### Performance Profiling
```typescript
// React Profiler for component performance
import { Profiler } from 'react'

const onRenderCallback = (id, phase, actualDuration) => {
  console.log('Component:', id, 'Phase:', phase, 'Duration:', actualDuration)
}

<Profiler id="InteractiveMesh" onRender={onRenderCallback}>
  <InteractiveMeshBackground />
</Profiler>
```

### Memory Monitoring
```typescript
// Monitor memory usage
const monitorMemory = () => {
  if ('memory' in performance) {
    const memory = (performance as any).memory
    console.log({
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit
    })
  }
}

// Call periodically in development
useEffect(() => {
  const interval = setInterval(monitorMemory, 5000)
  return () => clearInterval(interval)
}, [])
```

## 🔧 Build Optimization

### Production Build Configuration
```javascript
// next.config.mjs
const nextConfig = {
  // Optimize for production
  swcMinify: true,
  compress: true,
  
  // Image optimization
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
  },
  
  // Experimental optimizations
  experimental: {
    optimizeCss: true,
    optimizeServerReact: true,
  },
  
  // Webpack optimizations
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
      }
    }
    
    return config
  }
}
```

### Environment-Specific Optimizations
```typescript
// Conditional loading based on environment
const isDevelopment = process.env.NODE_ENV === 'development'

// Only load dev tools in development
const DevTools = isDevelopment 
  ? dynamic(() => import('./dev-tools'))
  : () => null

// Optimize for production
const optimizedConfig = {
  enableAnalytics: !isDevelopment,
  enableDebugMode: isDevelopment,
  logLevel: isDevelopment ? 'debug' : 'error'
}
```

## 📈 Performance Testing

### Lighthouse CI Integration
```yaml
# .github/workflows/lighthouse.yml
name: Lighthouse CI
on: [push]
jobs:
  lighthouse:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run Lighthouse CI
        run: |
          npm install -g @lhci/cli@0.12.x
          lhci autorun
```

### Performance Budgets
```json
// lighthouserc.json
{
  "ci": {
    "assert": {
      "assertions": {
        "categories:performance": ["error", {"minScore": 0.9}],
        "categories:accessibility": ["error", {"minScore": 0.9}],
        "categories:best-practices": ["error", {"minScore": 0.9}],
        "categories:seo": ["error", {"minScore": 0.9}]
      }
    }
  }
}
```

### Load Testing
```typescript
// Performance testing with Playwright
import { test, expect } from '@playwright/test'

test('landing page performance', async ({ page }) => {
  await page.goto('/')
  
  // Measure LCP
  const lcp = await page.evaluate(() => {
    return new Promise((resolve) => {
      new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        resolve(lastEntry.startTime)
      }).observe({ entryTypes: ['largest-contentful-paint'] })
    })
  })
  
  expect(lcp).toBeLessThan(2500) // 2.5 seconds
})
```

## 🎯 Optimization Checklist

### Pre-deployment Checklist
- [ ] Bundle size analysis completed
- [ ] Lighthouse scores > 90
- [ ] Core Web Vitals within targets
- [ ] Memory leaks checked
- [ ] Performance profiling completed
- [ ] Image optimization verified
- [ ] Font loading optimized
- [ ] Third-party scripts audited
- [ ] Caching strategies implemented
- [ ] Error boundaries in place

### Ongoing Monitoring
- [ ] Performance metrics dashboard
- [ ] Real User Monitoring (RUM)
- [ ] Automated performance testing
- [ ] Regular bundle analysis
- [ ] Memory usage monitoring
- [ ] Core Web Vitals tracking
- [ ] User experience metrics

---

*This performance guide ensures the ConTXT frontend maintains optimal performance while delivering rich interactive experiences.*
