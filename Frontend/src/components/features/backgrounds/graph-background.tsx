"use client"

import { useEffect, useRef, useCallback, useState } from "react"
import { useTheme } from "next-themes"

interface GraphNode {
  id: string
  x: number
  y: number
  vx: number
  vy: number
  radius: number
  connections: string[]
  type: "primary" | "secondary" | "accent"
}

interface GraphEdge {
  source: string
  target: string
  strength: number
}

interface GraphBackgroundProps {
  nodeCount?: number
  animationSpeed?: number
  className?: string
}

const REPULSION_STRENGTH = 1000
const ATTRACTION_STRENGTH = 0.001
const DAMPING = 0.85
const MIN_DISTANCE = 50

export default function GraphBackground({
  nodeCount = 20,
  animationSpeed = 0.3,
  className = "",
}: GraphBackgroundProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const { theme } = useTheme()
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 })
  const [isClient, setIsClient] = useState(false)
  const [canvasError, setCanvasError] = useState<string | null>(null)

  // Initialize nodes and edges
  const initializeGraph = useCallback(
    (width: number, height: number): { nodes: GraphNode[]; edges: GraphEdge[] } => {
      const nodes: GraphNode[] = []
      const edges: GraphEdge[] = []

      // Create nodes
      for (let i = 0; i < nodeCount; i++) {
        const nodeType = i < 3 ? "primary" : i < nodeCount * 0.7 ? "secondary" : "accent"
        nodes.push({
          id: `node-${i}`,
          x: Math.random() * width,
          y: Math.random() * height,
          vx: (Math.random() - 0.5) * 2,
          vy: (Math.random() - 0.5) * 2,
          radius: nodeType === "primary" ? 6 : nodeType === "secondary" ? 4 : 3,
          connections: [],
          type: nodeType,
        })
      }

      // Create edges with 35% connection density
      for (let i = 0; i < nodes.length; i++) {
        for (let j = i + 1; j < nodes.length; j++) {
          if (Math.random() < 0.35) {
            const strength = Math.random() * 0.5 + 0.5
            edges.push({
              source: nodes[i].id,
              target: nodes[j].id,
              strength,
            })
            nodes[i].connections.push(nodes[j].id)
            nodes[j].connections.push(nodes[i].id)
          }
        }
      }

      return { nodes, edges }
    },
    [nodeCount],
  )

  // Force calculation
  const calculateForces = useCallback(
    (nodes: GraphNode[], edges: GraphEdge[]) => {
      // Reset velocities
      nodes.forEach((node) => {
        node.vx *= DAMPING
        node.vy *= DAMPING
      })

      // Repulsion force between all nodes
      nodes.forEach((nodeA) => {
        nodes.forEach((nodeB) => {
          if (nodeA.id !== nodeB.id) {
            const dx = nodeA.x - nodeB.x
            const dy = nodeA.y - nodeB.y
            const distance = Math.sqrt(dx * dx + dy * dy) + 0.1

            if (distance < MIN_DISTANCE) {
              const repulsion = REPULSION_STRENGTH / (distance * distance)
              nodeA.vx += (dx / distance) * repulsion * animationSpeed
              nodeA.vy += (dy / distance) * repulsion * animationSpeed
            }
          }
        })
      })

      // Attraction force for connected nodes
      edges.forEach((edge) => {
        const source = nodes.find((n) => n.id === edge.source)
        const target = nodes.find((n) => n.id === edge.target)

        if (source && target) {
          const dx = target.x - source.x
          const dy = target.y - source.y
          const distance = Math.sqrt(dx * dx + dy * dy)
          const attraction = distance * ATTRACTION_STRENGTH * edge.strength

          source.vx += (dx / distance) * attraction * animationSpeed
          source.vy += (dy / distance) * attraction * animationSpeed
          target.vx -= (dx / distance) * attraction * animationSpeed
          target.vy -= (dy / distance) * attraction * animationSpeed
        }
      })
    },
    [animationSpeed],
  )

  // Update positions
  const updatePositions = useCallback((nodes: GraphNode[], width: number, height: number) => {
    nodes.forEach((node) => {
      node.x += node.vx
      node.y += node.vy

      // Boundary conditions with soft bounce
      if (node.x < node.radius) {
        node.x = node.radius
        node.vx *= -0.8
      }
      if (node.x > width - node.radius) {
        node.x = width - node.radius
        node.vx *= -0.8
      }
      if (node.y < node.radius) {
        node.y = node.radius
        node.vy *= -0.8
      }
      if (node.y > height - node.radius) {
        node.y = height - node.radius
        node.vy *= -0.8
      }
    })
  }, [])

  // Render functions
  const renderConnections = useCallback(
    (ctx: CanvasRenderingContext2D, nodes: GraphNode[], edges: GraphEdge[]) => {
      const isDark = theme === "dark"
      ctx.strokeStyle = isDark ? "rgba(239, 68, 68, 0.15)" : "rgba(239, 68, 68, 0.25)"
      ctx.lineWidth = 1

      edges.forEach((edge) => {
        const source = nodes.find((n) => n.id === edge.source)
        const target = nodes.find((n) => n.id === edge.target)

        if (source && target) {
          const distance = Math.sqrt(Math.pow(target.x - source.x, 2) + Math.pow(target.y - source.y, 2))

          // Only render connections within reasonable distance
          if (distance < 200) {
            ctx.globalAlpha = Math.max(0.1, 1 - distance / 200) * edge.strength
            ctx.beginPath()
            ctx.moveTo(source.x, source.y)

            // Create curved path
            const midX = (source.x + target.x) / 2
            const midY = (source.y + target.y) / 2
            const offset = (Math.random() - 0.5) * 20
            ctx.quadraticCurveTo(midX + offset, midY + offset, target.x, target.y)
            ctx.stroke()
          }
        }
      })
      ctx.globalAlpha = 1
    },
    [theme],
  )

  const renderNodes = useCallback(
    (ctx: CanvasRenderingContext2D, nodes: GraphNode[]) => {
      const isDark = theme === "dark"

      nodes.forEach((node) => {
        // Node colors based on type and theme
        let fillColor: string
        let glowColor: string

        switch (node.type) {
          case "primary":
            fillColor = "#ef4444"
            glowColor = "rgba(239, 68, 68, 0.4)"
            break
          case "secondary":
            fillColor = isDark ? "#f3f4f6" : "#ffffff"
            glowColor = isDark ? "rgba(243, 244, 246, 0.3)" : "rgba(255, 255, 255, 0.6)"
            break
          case "accent":
            fillColor = isDark ? "#374151" : "#6b7280"
            glowColor = isDark ? "rgba(55, 65, 81, 0.4)" : "rgba(107, 114, 128, 0.4)"
            break
        }

        // Draw glow effect
        ctx.shadowColor = glowColor
        ctx.shadowBlur = node.radius * 2
        ctx.fillStyle = fillColor

        ctx.beginPath()
        ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2)
        ctx.fill()

        // Reset shadow
        ctx.shadowBlur = 0
      })
    },
    [theme],
  )

  // Main render loop
  const renderLoop = useCallback(() => {
    if (!isClient || canvasError) return

    const canvas = canvasRef.current
    if (!canvas) return

    let ctx: CanvasRenderingContext2D | null = null
    try {
      ctx = canvas.getContext("2d")
      if (!ctx) {
        setCanvasError("Failed to get 2D context")
        return
      }
    } catch (error) {
      setCanvasError(`Canvas context error: ${error instanceof Error ? error.message : 'Unknown error'}`)
      return
    }

    const { width, height } = dimensions
    if (width === 0 || height === 0) return

    // Clear with gradient background
    const gradient = ctx.createLinearGradient(0, 0, width, height)
    const isDark = theme === "dark"

    if (isDark) {
      gradient.addColorStop(0, "#111827")
      gradient.addColorStop(1, "#1f2937")
    } else {
      gradient.addColorStop(0, "#ffffff")
      gradient.addColorStop(1, "#f9fafb")
    }

    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, width, height)

    // Get current graph state
    const graphData = canvas.dataset.graphData
    if (graphData) {
      const { nodes, edges } = JSON.parse(graphData)

      // Update simulation
      calculateForces(nodes, edges)
      updatePositions(nodes, width, height)

      // Render
      renderConnections(ctx, nodes, edges)
      renderNodes(ctx, nodes)

      // Store updated data
      canvas.dataset.graphData = JSON.stringify({ nodes, edges })
    }

    animationRef.current = requestAnimationFrame(renderLoop)
  }, [isClient, canvasError, dimensions, theme, calculateForces, updatePositions, renderConnections, renderNodes])

  // Client-side only initialization
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Handle resize
  useEffect(() => {
    if (!isClient) return

    const handleResize = () => {
      if (canvasRef.current) {
        const rect = canvasRef.current.getBoundingClientRect()
        setDimensions({ width: rect.width, height: rect.height })
      }
    }

    handleResize()
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [isClient])

  // Initialize graph and start animation
  useEffect(() => {
    if (!isClient || canvasError || dimensions.width <= 0 || dimensions.height <= 0) return

    const canvas = canvasRef.current
    if (!canvas) return

    try {
      canvas.width = dimensions.width
      canvas.height = dimensions.height

      // Test canvas context creation
      const ctx = canvas.getContext("2d")
      if (!ctx) {
        setCanvasError("Failed to create canvas context")
        return
      }

      const { nodes, edges } = initializeGraph(dimensions.width, dimensions.height)
      canvas.dataset.graphData = JSON.stringify({ nodes, edges })

      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
      renderLoop()
    } catch (error) {
      setCanvasError(`Canvas initialization error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [isClient, canvasError, dimensions, initializeGraph, renderLoop])

  // Show error state or loading state for SSR
  if (canvasError) {
    return (
      <div
        className={`absolute inset-0 ${className} flex items-center justify-center bg-black`}
        style={{ width: "100%", height: "100%" }}
      >
        <div className="text-red-400 text-sm opacity-50">
          Canvas Error: {canvasError}
        </div>
      </div>
    )
  }

  if (!isClient) {
    return (
      <div
        className={`absolute inset-0 ${className}`}
        style={{
          width: "100%",
          height: "100%",
          background: "transparent",
        }}
      />
    )
  }

  return (
    <canvas ref={canvasRef} className={`absolute inset-0 ${className}`} style={{ width: "100%", height: "100%" }} />
  )
}
