"use client"

import { useEffect, useRef, useCallback, useState } from "react"

interface MeshNode {
  id: string
  x: number
  y: number
  originalX: number
  originalY: number
  vx: number
  vy: number
  radius: number
  connections: string[]
  glowIntensity: number
  pulsePhase: number
  gridX: number
  gridY: number
  isVisible: boolean
}

interface MeshConnection {
  source: string
  target: string
  distance: number
  opacity: number
  isVisible: boolean
}

interface Particle {
  x: number
  y: number
  speed: number
  connection: string
  progress: number
  size: number
  opacity: number
  id: string
  isActive: boolean
}

interface SpatialGrid {
  [key: string]: MeshNode[]
}

interface InteractiveMeshBackgroundProps {
  className?: string
}

// Optimized constants
const GRAVITY_STRENGTH = 300
const GRAVITY_FORCE = 0.08
const DAMPING = 0.94
const RETURN_FORCE = 0.008
const MAX_CONNECTION_DISTANCE = 120
const PARTICLE_SPAWN_RATE = 0.06
const GRID_SIZE = 80 // For spatial partitioning
const VIEWPORT_PADDING = 50

export default function InteractiveMeshBackground({ className = "" }: InteractiveMeshBackgroundProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const mouseRef = useRef({ x: 0, y: 0, isActive: false })
  const nodesRef = useRef<MeshNode[]>([])
  const connectionsRef = useRef<MeshConnection[]>([])
  const particlesRef = useRef<Particle[]>([])
  const particlePoolRef = useRef<Particle[]>([]) // Object pool
  const spatialGridRef = useRef<SpatialGrid>({})
  const lastUpdateRef = useRef(0)
  const frameCountRef = useRef(0)
  const viewportRef = useRef({ x: 0, y: 0, width: 0, height: 0 })

  const [dimensions, setDimensions] = useState({ width: 0, height: 0 })
  const [deviceType, setDeviceType] = useState<"desktop" | "tablet" | "mobile">("desktop")
  const [isClient, setIsClient] = useState(false)
  const [canvasError, setCanvasError] = useState<string | null>(null)

  // Detect device type
  const detectDeviceType = useCallback(() => {
    const width = window.innerWidth
    if (width >= 1024) return "desktop"
    if (width >= 768) return "tablet"
    return "mobile"
  }, [])

  // Restored higher counts with better optimization
  const getDeviceConfig = useCallback(() => {
    const configs = {
      desktop: {
        nodeCount: 120, // High count but optimized
        maxParticles: 50,
        maxConnections: 80,
        connectionDensity: 0.5,
      },
      tablet: {
        nodeCount: 80,
        maxParticles: 35,
        maxConnections: 60,
        connectionDensity: 0.4,
      },
      mobile: {
        nodeCount: 60,
        maxParticles: 25,
        maxConnections: 40,
        connectionDensity: 0.3,
      },
    }
    return configs[deviceType]
  }, [deviceType])

  // Spatial grid helpers
  const getGridKey = useCallback((x: number, y: number) => {
    const gridX = Math.floor(x / GRID_SIZE)
    const gridY = Math.floor(y / GRID_SIZE)
    return `${gridX},${gridY}`
  }, [])

  const updateSpatialGrid = useCallback(() => {
    const grid: SpatialGrid = {}
    const nodes = nodesRef.current

    nodes.forEach((node) => {
      const key = getGridKey(node.x, node.y)
      if (!grid[key]) grid[key] = []
      grid[key].push(node)
      node.gridX = Math.floor(node.x / GRID_SIZE)
      node.gridY = Math.floor(node.y / GRID_SIZE)
    })

    spatialGridRef.current = grid
  }, [getGridKey])

  // Get nearby nodes using spatial partitioning
  const getNearbyNodes = useCallback((node: MeshNode): MeshNode[] => {
    const nearby: MeshNode[] = []
    const grid = spatialGridRef.current

    // Check current and adjacent grid cells
    for (let dx = -1; dx <= 1; dx++) {
      for (let dy = -1; dy <= 1; dy++) {
        const key = `${node.gridX + dx},${node.gridY + dy}`
        if (grid[key]) {
          nearby.push(...grid[key])
        }
      }
    }

    return nearby.filter((n) => n.id !== node.id)
  }, [])

  // Object pool for particles
  const getParticleFromPool = useCallback((): Particle => {
    const pool = particlePoolRef.current
    if (pool.length > 0) {
      const particle = pool.pop()!
      particle.isActive = true
      return particle
    }

    return {
      x: 0,
      y: 0,
      speed: 0,
      connection: "",
      progress: 0,
      size: 0,
      opacity: 0,
      id: `particle-${Date.now()}-${Math.random()}`,
      isActive: true,
    }
  }, [])

  const returnParticleToPool = useCallback((particle: Particle) => {
    particle.isActive = false
    particlePoolRef.current.push(particle)
  }, [])

  // Viewport culling
  const updateViewport = useCallback(() => {
    const { width, height } = dimensions
    viewportRef.current = {
      x: -VIEWPORT_PADDING,
      y: -VIEWPORT_PADDING,
      width: width + VIEWPORT_PADDING * 2,
      height: height + VIEWPORT_PADDING * 2,
    }
  }, [dimensions])

  const isInViewport = useCallback((x: number, y: number) => {
    const viewport = viewportRef.current
    return x >= viewport.x && x <= viewport.x + viewport.width && y >= viewport.y && y <= viewport.y + viewport.height
  }, [])

  // Optimized node initialization
  const initializeNodes = useCallback(() => {
    const { width, height } = dimensions
    if (width === 0 || height === 0) return

    const config = getDeviceConfig()
    const nodes: MeshNode[] = []

    // Pre-allocate particle pool
    particlePoolRef.current = []
    for (let i = 0; i < config.maxParticles * 2; i++) {
      particlePoolRef.current.push({
        x: 0,
        y: 0,
        speed: 0,
        connection: "",
        progress: 0,
        size: 0,
        opacity: 0,
        id: `pooled-${i}`,
        isActive: false,
      })
    }

    // Create nodes with better distribution
    for (let i = 0; i < config.nodeCount; i++) {
      const padding = 80
      const x = padding + Math.random() * (width - padding * 2)
      const y = padding + Math.random() * (height - padding * 2)

      nodes.push({
        id: `node-${i}`,
        x,
        y,
        originalX: x,
        originalY: y,
        vx: (Math.random() - 0.5) * 0.5,
        vy: (Math.random() - 0.5) * 0.5,
        radius: Math.random() * 1.2 + 0.8,
        connections: [],
        glowIntensity: Math.random() * 0.3 + 0.2,
        pulsePhase: Math.random() * Math.PI * 2,
        gridX: 0,
        gridY: 0,
        isVisible: true,
      })
    }

    nodesRef.current = nodes
    connectionsRef.current = []
    particlesRef.current = []
    updateViewport()
  }, [dimensions, deviceType, getDeviceConfig, updateViewport])

  // Optimized connection management with spatial partitioning
  const updateConnections = useCallback(() => {
    const nodes = nodesRef.current
    const connections = connectionsRef.current
    const config = getDeviceConfig()

    // Only update every 5 frames for better performance
    if (frameCountRef.current % 5 !== 0) return

    // Update spatial grid
    updateSpatialGrid()

    // Clear old connections
    connections.length = 0
    nodes.forEach((node) => {
      node.connections = []
      node.isVisible = isInViewport(node.x, node.y)
    })

    // Create connections using spatial partitioning
    for (let i = 0; i < nodes.length && connections.length < config.maxConnections; i++) {
      const nodeA = nodes[i]
      if (nodeA.connections.length >= 3 || !nodeA.isVisible) continue

      const nearbyNodes = getNearbyNodes(nodeA)

      for (const nodeB of nearbyNodes) {
        if (connections.length >= config.maxConnections) break
        if (nodeB.connections.length >= 3 || !nodeB.isVisible) continue

        const dx = nodeB.x - nodeA.x
        const dy = nodeB.y - nodeA.y
        const distanceSquared = dx * dx + dy * dy

        if (distanceSquared < MAX_CONNECTION_DISTANCE * MAX_CONNECTION_DISTANCE) {
          if (Math.random() < config.connectionDensity) {
            const distance = Math.sqrt(distanceSquared)
            connections.push({
              source: nodeA.id,
              target: nodeB.id,
              distance,
              opacity: Math.max(0.1, 0.4 * (1 - distance / MAX_CONNECTION_DISTANCE)),
              isVisible: true,
            })
            nodeA.connections.push(nodeB.id)
            nodeB.connections.push(nodeA.id)
          }
        }
      }
    }

    // Update connection visibility
    connections.forEach((connection) => {
      const sourceNode = nodes.find((n) => n.id === connection.source)
      const targetNode = nodes.find((n) => n.id === connection.target)
      connection.isVisible = !!(sourceNode?.isVisible && targetNode?.isVisible)
    })
  }, [getDeviceConfig, updateSpatialGrid, getNearbyNodes, isInViewport])

  // Optimized physics with better performance
  const updatePhysics = useCallback(() => {
    const nodes = nodesRef.current
    const mouse = mouseRef.current
    const time = Date.now() * 0.0005
    const { width, height } = dimensions

    // Batch process nodes
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i]

      // Skip invisible nodes
      if (!node.isVisible) continue

      // Simplified floating movement
      const floatX = Math.sin(time + node.pulsePhase) * 8
      const floatY = Math.cos(time * 1.1 + node.pulsePhase) * 6

      // Optimized mouse interaction
      if (mouse.isActive) {
        const dx = mouse.x - node.x
        const dy = mouse.y - node.y
        const distanceSquared = dx * dx + dy * dy

        if (distanceSquared < GRAVITY_STRENGTH * GRAVITY_STRENGTH) {
          const distance = Math.sqrt(distanceSquared)
          const force = GRAVITY_FORCE / (distance + 1)
          node.vx += (dx / distance) * force
          node.vy += (dy / distance) * force
        }
      }

      // Return to floating position
      const targetX = node.originalX + floatX
      const targetY = node.originalY + floatY
      node.vx += (targetX - node.x) * RETURN_FORCE
      node.vy += (targetY - node.y) * RETURN_FORCE

      // Apply damping and update position
      node.vx *= DAMPING
      node.vy *= DAMPING
      node.x += node.vx
      node.y += node.vy

      // Boundary checking
      if (node.x < 0 || node.x > width) node.vx *= -0.5
      if (node.y < 0 || node.y > height) node.vy *= -0.5
      node.x = Math.max(0, Math.min(width, node.x))
      node.y = Math.max(0, Math.min(height, node.y))

      // Update glow
      node.glowIntensity = 0.2 + 0.2 * Math.sin(time * 2 + node.pulsePhase)
    }
  }, [dimensions])

  // Optimized particle system with object pooling
  const updateParticles = useCallback(() => {
    const particles = particlesRef.current
    const connections = connectionsRef.current
    const nodes = nodesRef.current
    const config = getDeviceConfig()

    // Update existing particles
    for (let i = particles.length - 1; i >= 0; i--) {
      const particle = particles[i]
      if (!particle.isActive) continue

      particle.progress += particle.speed * 0.02

      if (particle.progress >= 1) {
        returnParticleToPool(particle)
        particles.splice(i, 1)
        continue
      }

      const connection = connections.find(
        (c) =>
          c.isVisible &&
          (`${c.source}-${c.target}` === particle.connection || `${c.target}-${c.source}` === particle.connection),
      )

      if (!connection) {
        returnParticleToPool(particle)
        particles.splice(i, 1)
        continue
      }

      const sourceNode = nodes.find((n) => n.id === connection.source)
      const targetNode = nodes.find((n) => n.id === connection.target)

      if (!sourceNode || !targetNode) {
        returnParticleToPool(particle)
        particles.splice(i, 1)
        continue
      }

      // Update position
      particle.x = sourceNode.x + (targetNode.x - sourceNode.x) * particle.progress
      particle.y = sourceNode.y + (targetNode.y - sourceNode.y) * particle.progress
      particle.opacity = Math.sin(particle.progress * Math.PI) * 0.6
    }

    // Spawn new particles using object pool
    if (particles.length < config.maxParticles && Math.random() < PARTICLE_SPAWN_RATE) {
      const visibleConnections = connections.filter((c) => c.isVisible)
      if (visibleConnections.length > 0) {
        const connection = visibleConnections[Math.floor(Math.random() * visibleConnections.length)]
        const sourceNode = nodes.find((n) => n.id === connection.source)

        if (sourceNode) {
          const particle = getParticleFromPool()
          particle.x = sourceNode.x
          particle.y = sourceNode.y
          particle.speed = Math.random() * 0.5 + 0.3
          particle.connection = `${connection.source}-${connection.target}`
          particle.progress = 0
          particle.size = Math.random() * 0.8 + 0.4
          particle.opacity = 0
          particles.push(particle)
        }
      }
    }
  }, [getDeviceConfig, getParticleFromPool, returnParticleToPool])

  // Optimized rendering with instancing
  const renderConnections = useCallback((ctx: CanvasRenderingContext2D) => {
    const connections = connectionsRef.current.filter((c) => c.isVisible)
    const nodes = nodesRef.current

    if (connections.length === 0) return

    ctx.strokeStyle = "rgba(239, 68, 68, 0.2)"
    ctx.lineWidth = 1

    // Batch render all connections
    ctx.beginPath()
    connections.forEach((connection) => {
      const sourceNode = nodes.find((n) => n.id === connection.source)
      const targetNode = nodes.find((n) => n.id === connection.target)

      if (!sourceNode || !targetNode) return

      ctx.globalAlpha = connection.opacity
      ctx.moveTo(sourceNode.x, sourceNode.y)
      ctx.lineTo(targetNode.x, targetNode.y)
    })
    ctx.stroke()
    ctx.globalAlpha = 1
  }, [])

  const renderNodes = useCallback((ctx: CanvasRenderingContext2D) => {
    const nodes = nodesRef.current.filter((n) => n.isVisible)

    // Pre-create gradient for reuse
    const gradientCache = new Map<string, CanvasGradient>()

    nodes.forEach((node) => {
      // Use cached gradient or create new one
      const gradientKey = `${Math.floor(node.glowIntensity * 10)}`
      let gradient = gradientCache.get(gradientKey)

      if (!gradient) {
        gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, node.radius + 2)
        gradient.addColorStop(0, `rgba(239, 68, 68, ${node.glowIntensity})`)
        gradient.addColorStop(1, "rgba(239, 68, 68, 0)")
        gradientCache.set(gradientKey, gradient)
      }

      ctx.save()
      ctx.translate(node.x, node.y)
      ctx.fillStyle = gradient
      ctx.beginPath()
      ctx.arc(0, 0, node.radius + 2, 0, Math.PI * 2)
      ctx.fill()

      // Node core
      ctx.fillStyle = `rgba(220, 38, 38, 0.8)`
      ctx.beginPath()
      ctx.arc(0, 0, node.radius, 0, Math.PI * 2)
      ctx.fill()
      ctx.restore()
    })
  }, [])

  const renderParticles = useCallback((ctx: CanvasRenderingContext2D) => {
    const particles = particlesRef.current.filter((p) => p.isActive && p.opacity > 0)

    particles.forEach((particle) => {
      ctx.fillStyle = `rgba(252, 165, 165, ${particle.opacity})`
      ctx.beginPath()
      ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
      ctx.fill()
    })
  }, [])

  // Optimized render loop with temporal distribution
  const renderFrame = useCallback(() => {
    // Skip rendering if not on client or if there's a canvas error
    if (!isClient || canvasError) return

    const canvas = canvasRef.current
    if (!canvas) return

    let ctx: CanvasRenderingContext2D | null = null
    try {
      ctx = canvas.getContext("2d")
      if (!ctx) {
        setCanvasError("Failed to get 2D context")
        return
      }
    } catch (error) {
      setCanvasError(`Canvas context error: ${error instanceof Error ? error.message : 'Unknown error'}`)
      return
    }

    frameCountRef.current++

    try {
      // Clear canvas efficiently
      ctx.fillStyle = "rgba(0, 0, 0, 0.1)"
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // Distribute updates across frames
      updatePhysics()

      // Update connections less frequently
      updateConnections()

      // Update particles every other frame
      if (frameCountRef.current % 2 === 0) {
        updateParticles()
      }

      // Render in optimal order (back to front)
      renderConnections(ctx)
      renderNodes(ctx)
      renderParticles(ctx)

      animationRef.current = requestAnimationFrame(renderFrame)
    } catch (error) {
      setCanvasError(`Rendering error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }, [isClient, canvasError, updatePhysics, updateConnections, updateParticles, renderConnections, renderNodes, renderParticles])

  // Mouse handlers
  const handleMouseMove = useCallback((event: MouseEvent) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const rect = canvas.getBoundingClientRect()
    mouseRef.current = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top,
      isActive: true,
    }
  }, [])

  const handleMouseLeave = useCallback(() => {
    mouseRef.current.isActive = false
  }, [])

  // Client-side only initialization
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Handle resize
  useEffect(() => {
    if (!isClient) return

    const handleResize = () => {
      if (canvasRef.current) {
        const rect = canvasRef.current.getBoundingClientRect()
        setDimensions({ width: rect.width, height: rect.height })
        setDeviceType(detectDeviceType())
      }
    }

    handleResize()
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [isClient, detectDeviceType])

  // Initialize
  useEffect(() => {
    if (!isClient || canvasError || dimensions.width <= 0 || dimensions.height <= 0) return

    const canvas = canvasRef.current
    if (!canvas) return

    try {
      canvas.width = dimensions.width
      canvas.height = dimensions.height

      // Test canvas context creation
      const ctx = canvas.getContext("2d")
      if (!ctx) {
        setCanvasError("Failed to create canvas context")
        return
      }

      initializeNodes()

      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
      renderFrame()
    } catch (error) {
      setCanvasError(`Canvas initialization error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [isClient, canvasError, dimensions, initializeNodes, renderFrame])

  // Add event listeners
  useEffect(() => {
    if (!isClient || canvasError) return

    const canvas = canvasRef.current
    if (!canvas) return

    canvas.addEventListener("mousemove", handleMouseMove)
    canvas.addEventListener("mouseleave", handleMouseLeave)

    return () => {
      canvas.removeEventListener("mousemove", handleMouseMove)
      canvas.removeEventListener("mouseleave", handleMouseLeave)
    }
  }, [isClient, canvasError, handleMouseMove, handleMouseLeave])

  // Show error state or loading state for SSR
  if (canvasError) {
    return (
      <div
        className={`absolute inset-0 ${className} flex items-center justify-center bg-black`}
        style={{ width: "100%", height: "100%" }}
      >
        <div className="text-red-400 text-sm opacity-50">
          Canvas Error: {canvasError}
        </div>
      </div>
    )
  }

  if (!isClient) {
    return (
      <div
        className={`absolute inset-0 ${className}`}
        style={{
          width: "100%",
          height: "100%",
          background: "#000000",
        }}
      />
    )
  }

  return (
    <canvas
      ref={canvasRef}
      className={`absolute inset-0 ${className}`}
      style={{
        width: "100%",
        height: "100%",
        background: "#000000",
      }}
    />
  )
}
