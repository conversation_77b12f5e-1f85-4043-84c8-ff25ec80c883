"use client"

import type React from "react"

import { useState } from "react"
import { ArrowRight, Brain, Code, Database, Sparkles, Check, X, Star, Zap, Shield, Users } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { InteractiveMeshBackground } from "@/components/features/backgrounds"

export default function ConTXTLanding() {
  const [email, setEmail] = useState("")
  const [isAnnual, setIsAnnual] = useState(false)

  const features = [
    {
      icon: Brain,
      title: "AI Context Engineering",
      description:
        "Transform unstructured data into intelligent, actionable context for AI tools like Cursor, Windsurf, and Claude.",
      metric: "10x faster",
    },
    {
      icon: Database,
      title: "Knowledge Graph Intelligence",
      description:
        "Build dynamic knowledge graphs that understand relationships and provide semantic search capabilities.",
      metric: "99.9% accuracy",
    },
    {
      icon: Code,
      title: "Developer-First Integration",
      description: "Seamlessly integrate with your existing workflow through APIs, CLI tools, and IDE extensions.",
      metric: "< 5min setup",
    },
  ]

  const stats = [
    { value: "50K+", label: "Developers" },
    { value: "1M+", label: "Context Graphs" },
    { value: "99.9%", label: "Uptime" },
    { value: "< 100ms", label: "Response Time" },
  ]

  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Email submitted:", email)
    setEmail("")
  }

  return (
    <div className="relative min-h-screen overflow-hidden bg-black">
      {/* Interactive Mesh Background */}
      <InteractiveMeshBackground className="absolute inset-0 z-0" />

      {/* Dark Overlay for Content Readability */}
      <div className="absolute inset-0 bg-black/20 z-10" />

      {/* Main Content */}
      <div className="relative z-20">
        {/* Navigation */}
        <nav className="absolute top-0 left-0 right-0 z-30 p-6">
          <div className="max-w-7xl mx-auto flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg shadow-red-500/25">
                <Brain className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold text-white">ConTXT</span>
            </div>

            <div className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-gray-300 hover:text-red-400 transition-colors duration-200">
                Features
              </a>
              <a href="#pricing" className="text-gray-300 hover:text-red-400 transition-colors duration-200">
                Pricing
              </a>
              <a href="#docs" className="text-gray-300 hover:text-red-400 transition-colors duration-200">
                Docs
              </a>
              <Button
                variant="outline"
                size="sm"
                className="border-gray-600 text-gray-300 hover:bg-gray-800 bg-transparent"
              >
                Sign In
              </Button>
              <Button size="sm" className="bg-red-600 hover:bg-red-700 shadow-lg shadow-red-600/25">
                Get Started
              </Button>
            </div>
          </div>
        </nav>

        {/* Hero Section */}
        <section className="flex flex-col justify-center min-h-screen px-6 lg:px-12">
          <div className="max-w-6xl mx-auto text-center text-white">
            <Badge variant="secondary" className="mb-8 bg-red-500/10 text-red-400 border-red-500/20 backdrop-blur-sm">
              <Sparkles className="w-4 h-4 mr-2" />
              Enhanced Interactive AI Context Engineering
            </Badge>

            <h1 className="text-6xl md:text-8xl font-bold mb-8 leading-tight">
              Transform Your Data Into
              <span className="block text-transparent bg-gradient-to-r from-red-400 via-red-500 to-red-600 bg-clip-text">
                AI-Ready Context
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-300 mb-12 leading-relaxed max-w-4xl mx-auto">
              Experience the enhanced power of dynamic knowledge graphs with our multi-layered interactive mesh
              visualization. Move your cursor to see how ConTXT's AI algorithms create intelligent connections with
              depth and dimension.
            </p>

            {/* Interactive Instructions */}
            <div className="mb-8 p-4 bg-red-500/10 border border-red-500/20 rounded-lg backdrop-blur-sm max-w-2xl mx-auto">
              <p className="text-red-300 text-sm">
                💡 <strong>Enhanced Experience:</strong> Move your mouse to see particles with random movement patterns,
                depth-based sizing, and evenly distributed spawning across the mesh network - just like ConTXT's
                intelligent data processing.
              </p>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-12">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl md:text-4xl font-bold text-red-400 mb-2">{stat.value}</div>
                  <div className="text-gray-400 text-sm uppercase tracking-wide">{stat.label}</div>
                </div>
              ))}
            </div>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
              <Button
                size="lg"
                className="bg-red-600 hover:bg-red-700 text-lg px-10 py-4 shadow-2xl shadow-red-600/25 hover:shadow-red-600/40 transition-all duration-300"
              >
                Start Building Context
                <ArrowRight className="ml-3 w-5 h-5" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="text-lg px-10 py-4 border-gray-600 text-gray-300 hover:bg-gray-800/50 backdrop-blur-sm bg-transparent"
              >
                View Interactive Demo
              </Button>
            </div>

            {/* Email Signup */}
            <form onSubmit={handleEmailSubmit} className="max-w-lg mx-auto">
              <div className="flex gap-3">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email for early access"
                  className="flex-1 px-6 py-4 rounded-xl border border-gray-700 bg-gray-900/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  required
                />
                <Button type="submit" className="bg-red-600 hover:bg-red-700 px-8 shadow-lg shadow-red-600/25">
                  Join Waitlist
                </Button>
              </div>
            </form>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="py-32 px-6 bg-gradient-to-b from-transparent to-gray-900/50">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-20">
              <Badge variant="secondary" className="mb-8 bg-red-500/10 text-red-400 border-red-500/20 backdrop-blur-sm">
                <Sparkles className="w-4 h-4 mr-2" />
                80+ Advanced Features
              </Badge>
              <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">Comprehensive AI Context Engineering</h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-12">
                Experience the full power of ConTXT's feature-rich platform with serverless scaling, enterprise
                security, and intelligent AI routing - all designed for production deployment at near-zero cost.
              </p>
            </div>

            {/* Core AI Features */}
            <div className="mb-20">
              <h3 className="text-3xl font-bold text-white mb-12 text-center">Core AI & Context Engineering</h3>
              <div className="grid md:grid-cols-3 gap-8">
                {/* Dynamic Context Building */}
                <div className="group relative p-8 rounded-2xl bg-gray-900/30 backdrop-blur-sm border border-gray-800 hover:border-red-500/50 transition-all duration-700 overflow-hidden hover:scale-105 hover:-translate-y-2 hover:shadow-2xl hover:shadow-red-500/20">
                  <div className="absolute inset-0 bg-gradient-to-br from-red-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700" />

                  {/* Enhanced Animated Background Elements */}
                  <div className="absolute inset-0 opacity-10 group-hover:opacity-30 transition-all duration-1000">
                    {/* Floating geometric shapes */}
                    <div className="absolute top-4 right-4 w-20 h-20">
                      <div className="w-full h-full bg-gradient-to-br from-red-400 to-red-600 rounded-lg transform rotate-12 group-hover:rotate-45 transition-transform duration-1000 animate-pulse" />
                      <div className="absolute top-3 left-3 w-8 h-8 bg-gradient-to-br from-red-300 to-red-500 rounded transform -rotate-12 group-hover:rotate-12 transition-transform duration-700" />
                      <div
                        className="absolute -top-2 -right-2 w-4 h-4 bg-red-400 rounded-full animate-bounce"
                        style={{ animationDelay: "0.5s" }}
                      />
                    </div>

                    {/* Particle system */}
                    <div className="absolute top-8 left-8">
                      <div className="w-1 h-1 bg-red-400 rounded-full animate-ping" />
                      <div
                        className="absolute top-4 left-6 w-1 h-1 bg-red-300 rounded-full animate-ping"
                        style={{ animationDelay: "0.3s" }}
                      />
                      <div
                        className="absolute -top-2 left-3 w-1 h-1 bg-red-500 rounded-full animate-ping"
                        style={{ animationDelay: "0.7s" }}
                      />
                    </div>

                    {/* Neural network lines */}
                    <div className="absolute bottom-4 left-4 right-4">
                      <div className="h-px bg-gradient-to-r from-transparent via-red-400 to-transparent opacity-50 group-hover:opacity-100 transition-opacity duration-700" />
                      <div className="h-px bg-gradient-to-r from-transparent via-red-300 to-transparent opacity-30 group-hover:opacity-80 transition-opacity duration-500 mt-2" />
                    </div>
                  </div>

                  <div className="relative z-10">
                    <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-125 group-hover:rotate-6 transition-all duration-500 shadow-lg group-hover:shadow-red-500/50">
                      <Brain className="w-8 h-8 text-white group-hover:animate-pulse" />
                    </div>
                    <h4 className="text-xl font-bold text-white mb-4 group-hover:text-red-300 transition-colors duration-300">
                      Dynamic Context Building
                    </h4>
                    <p className="text-gray-300 text-sm leading-relaxed group-hover:text-gray-200 transition-colors duration-300">
                      Automatically constructs and refines AI prompts using embedded knowledge graphs for precise
                      responses with intelligent routing and real-time optimization.
                    </p>

                    {/* Progress indicator */}
                    <div className="mt-4 h-1 bg-gray-700 rounded-full overflow-hidden">
                      <div className="h-full bg-gradient-to-r from-red-500 to-red-400 rounded-full w-0 group-hover:w-full transition-all duration-1000 ease-out" />
                    </div>
                  </div>
                </div>

                {/* Multi-Model Routing */}
                <div className="group relative p-8 rounded-2xl bg-gray-900/30 backdrop-blur-sm border border-gray-800 hover:border-blue-500/50 transition-all duration-700 overflow-hidden hover:scale-105 hover:-translate-y-2 hover:shadow-2xl hover:shadow-blue-500/20">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700" />

                  {/* Enhanced Network Animation */}
                  <div className="absolute inset-0 opacity-20 group-hover:opacity-50 transition-all duration-1000">
                    {/* Central hub */}
                    <div className="absolute top-1/2 right-8 transform -translate-y-1/2">
                      <div className="w-6 h-6 bg-blue-400 rounded-full animate-pulse relative">
                        {/* Connecting lines */}
                        <div className="absolute top-1/2 left-1/2 w-16 h-px bg-gradient-to-r from-blue-400 to-transparent transform -translate-y-1/2 rotate-45 group-hover:rotate-90 transition-transform duration-1000" />
                        <div className="absolute top-1/2 left-1/2 w-12 h-px bg-gradient-to-r from-blue-300 to-transparent transform -translate-y-1/2 -rotate-45 group-hover:-rotate-90 transition-transform duration-1000" />
                        <div className="absolute top-1/2 left-1/2 w-20 h-px bg-gradient-to-r from-blue-500 to-transparent transform -translate-y-1/2 rotate-12 group-hover:rotate-180 transition-transform duration-1200" />
                      </div>

                      {/* Satellite nodes */}
                      <div
                        className="absolute -top-4 -left-8 w-3 h-3 bg-blue-300 rounded-full animate-pulse"
                        style={{ animationDelay: "0.2s" }}
                      />
                      <div
                        className="absolute top-8 -left-4 w-2 h-2 bg-blue-500 rounded-full animate-pulse"
                        style={{ animationDelay: "0.5s" }}
                      />
                      <div
                        className="absolute -top-2 left-8 w-2 h-2 bg-blue-400 rounded-full animate-pulse"
                        style={{ animationDelay: "0.8s" }}
                      />
                    </div>

                    {/* Data flow particles */}
                    <div className="absolute top-6 left-6">
                      <div className="w-1 h-1 bg-blue-400 rounded-full animate-ping" />
                      <div
                        className="absolute top-2 left-4 w-1 h-1 bg-blue-300 rounded-full animate-ping"
                        style={{ animationDelay: "0.4s" }}
                      />
                      <div
                        className="absolute top-6 left-2 w-1 h-1 bg-blue-500 rounded-full animate-ping"
                        style={{ animationDelay: "0.8s" }}
                      />
                    </div>
                  </div>

                  <div className="relative z-10">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-125 group-hover:rotate-6 transition-all duration-500 shadow-lg group-hover:shadow-blue-500/50">
                      <Zap className="w-8 h-8 text-white group-hover:animate-bounce" />
                    </div>
                    <h4 className="text-xl font-bold text-white mb-4 group-hover:text-blue-300 transition-colors duration-300">
                      Multi-Model Routing
                    </h4>
                    <p className="text-gray-300 text-sm leading-relaxed group-hover:text-gray-200 transition-colors duration-300">
                      Intelligently routes queries to optimal models (Llama-3 8B, Claude Sonnet, GPT-4 Turbo) based on
                      complexity, cost optimization, and performance requirements.
                    </p>

                    {/* Model indicators */}
                    <div className="mt-4 flex space-x-2">
                      <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
                      <div
                        className="w-2 h-2 bg-blue-300 rounded-full animate-pulse"
                        style={{ animationDelay: "0.3s" }}
                      />
                      <div
                        className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"
                        style={{ animationDelay: "0.6s" }}
                      />
                    </div>
                  </div>
                </div>

                {/* Embedding-First RAG */}
                <div className="group relative p-8 rounded-2xl bg-gray-900/30 backdrop-blur-sm border border-gray-800 hover:border-green-500/50 transition-all duration-700 overflow-hidden hover:scale-105 hover:-translate-y-2 hover:shadow-2xl hover:shadow-green-500/20">
                  <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700" />

                  {/* Enhanced Search Wave Animation */}
                  <div className="absolute inset-0 opacity-20 group-hover:opacity-50 transition-all duration-1000">
                    {/* Concentric search waves */}
                    <div className="absolute top-6 right-6">
                      <div className="w-16 h-16 border-2 border-green-400 rounded-full animate-ping" />
                      <div
                        className="absolute top-2 left-2 w-12 h-12 border-2 border-green-300 rounded-full animate-ping"
                        style={{ animationDelay: "0.5s" }}
                      />
                      <div
                        className="absolute top-4 left-4 w-8 h-8 border border-green-500 rounded-full animate-ping"
                        style={{ animationDelay: "1s" }}
                      />
                      <div className="absolute top-6 left-6 w-4 h-4 bg-green-500 rounded-full animate-pulse" />
                    </div>

                    {/* Vector embeddings visualization */}
                    <div className="absolute bottom-8 left-8 right-8">
                      <div className="grid grid-cols-8 gap-1">
                        {Array.from({ length: 24 }).map((_, i) => (
                          <div
                            key={i}
                            className="w-1 bg-green-400 rounded-full animate-pulse"
                            style={{
                              height: `${Math.random() * 16 + 4}px`,
                              animationDelay: `${i * 0.1}s`,
                            }}
                          />
                        ))}
                      </div>
                    </div>

                    {/* Search beam */}
                    <div className="absolute top-1/2 left-4 right-4 transform -translate-y-1/2">
                      <div className="h-px bg-gradient-to-r from-green-400 via-green-300 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-1000" />
                    </div>
                  </div>

                  <div className="relative z-10">
                    <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-125 group-hover:rotate-6 transition-all duration-500 shadow-lg group-hover:shadow-green-500/50">
                      <Database
                        className="w-8 h-8 text-white group-hover:animate-spin"
                        style={{ animationDuration: "2s" }}
                      />
                    </div>
                    <h4 className="text-xl font-bold text-white mb-4 group-hover:text-green-300 transition-colors duration-300">
                      Embedding-First RAG
                    </h4>
                    <p className="text-gray-300 text-sm leading-relaxed group-hover:text-gray-200 transition-colors duration-300">
                      Low-cost semantic search with text-embedding-3-small, reducing completion costs by 80-90% through
                      intelligent retrieval and vector optimization.
                    </p>

                    {/* Cost savings indicator */}
                    <div className="mt-4 flex items-center space-x-2">
                      <span className="text-xs text-green-400 font-semibold">90% Cost Reduction</span>
                      <div className="flex-1 h-1 bg-gray-700 rounded-full overflow-hidden">
                        <div className="h-full bg-gradient-to-r from-green-500 to-green-400 rounded-full w-0 group-hover:w-[90%] transition-all duration-1500 ease-out" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Document Processing Features */}
            <div className="mb-20">
              <h3 className="text-3xl font-bold text-white mb-12 text-center">Document Processing & Ingestion</h3>
              <div className="grid md:grid-cols-4 gap-6">
                {[
                  {
                    icon: "📄",
                    title: "PDF Processor",
                    desc: "Extracts text, tables, images with OCR support and structure analysis",
                    color: "from-orange-500 to-orange-600",
                    hoverColor: "orange-500/50",
                    bgColor: "orange-500/5",
                    features: ["OCR Support", "Table Extraction", "Image Analysis"],
                  },
                  {
                    icon: "📊",
                    title: "CSV/JSON Handler",
                    desc: "Converts structured data to knowledge graphs with relationship mapping",
                    color: "from-purple-500 to-purple-600",
                    hoverColor: "purple-500/50",
                    bgColor: "purple-500/5",
                    features: ["Schema Detection", "Graph Mapping", "Data Validation"],
                  },
                  {
                    icon: "💻",
                    title: "Code Analyzer",
                    desc: "Parses 20+ languages, extracts dependencies and documentation",
                    color: "from-cyan-500 to-cyan-600",
                    hoverColor: "cyan-500/50",
                    bgColor: "cyan-500/5",
                    features: ["20+ Languages", "Dependency Tree", "Doc Generation"],
                  },
                  {
                    icon: "🌐",
                    title: "HTML Processor",
                    desc: "Scrapes and structures web content with semantic understanding",
                    color: "from-indigo-500 to-indigo-600",
                    hoverColor: "indigo-500/50",
                    bgColor: "indigo-500/5",
                    features: ["Smart Scraping", "Content Structure", "Link Analysis"],
                  },
                ].map((feature, index) => (
                  <div
                    key={index}
                    className={`group relative p-6 rounded-xl bg-gray-900/30 backdrop-blur-sm border border-gray-800 hover:border-${feature.hoverColor} transition-all duration-500 hover:scale-110 hover:-translate-y-3 hover:shadow-2xl overflow-hidden`}
                  >
                    {/* Animated background */}
                    <div
                      className={`absolute inset-0 bg-gradient-to-br ${feature.bgColor} opacity-0 group-hover:opacity-100 transition-opacity duration-500`}
                    />

                    {/* Floating particles */}
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-30 transition-opacity duration-700">
                      <div className="absolute top-2 right-2 w-1 h-1 bg-white rounded-full animate-ping" />
                      <div
                        className="absolute top-4 right-6 w-1 h-1 bg-white rounded-full animate-ping"
                        style={{ animationDelay: "0.3s" }}
                      />
                      <div
                        className="absolute top-6 right-4 w-1 h-1 bg-white rounded-full animate-ping"
                        style={{ animationDelay: "0.6s" }}
                      />
                    </div>

                    {/* Processing animation */}
                    <div className="absolute bottom-2 left-2 right-2 h-px bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-50 transition-opacity duration-1000" />

                    <div className="relative z-10 text-center">
                      <div className="text-4xl mb-4 group-hover:scale-150 group-hover:rotate-12 transition-all duration-500 inline-block">
                        {feature.icon}
                      </div>
                      <h4 className="text-lg font-bold text-white mb-3 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-white group-hover:to-gray-300 group-hover:bg-clip-text transition-all duration-300">
                        {feature.title}
                      </h4>
                      <p className="text-gray-400 text-sm mb-4 group-hover:text-gray-200 transition-colors duration-300">
                        {feature.desc}
                      </p>

                      {/* Feature tags */}
                      <div className="flex flex-wrap gap-1 justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-500 delay-200">
                        {feature.features.map((feat, i) => (
                          <span key={i} className="text-xs bg-white/10 px-2 py-1 rounded-full text-white">
                            {feat}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Security & Compliance Features */}
            <div className="mb-20">
              <h3 className="text-3xl font-bold text-white mb-12 text-center">Security & Compliance</h3>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {[
                  {
                    icon: Shield,
                    title: "End-to-End Encryption",
                    desc: "AES-256 for data at rest and TLS 1.3 in transit with zero-retention mode and quantum-resistant algorithms",
                    color: "from-red-500 to-red-600",
                    animation: "shield",
                    metrics: ["AES-256", "TLS 1.3", "Zero Retention"],
                  },
                  {
                    icon: Users,
                    title: "Role-Based Access Control",
                    desc: "Granular permissions with Admin, Editor, Viewer roles and comprehensive audit logging with real-time monitoring",
                    color: "from-blue-500 to-blue-600",
                    animation: "users",
                    metrics: ["3 Role Types", "Real-time Audit", "Granular Permissions"],
                  },
                  {
                    icon: Database,
                    title: "SOC 2 Compliance",
                    desc: "Type II reports, GDPR/HIPAA readiness with immutable audit trails and automated compliance monitoring",
                    color: "from-green-500 to-green-600",
                    animation: "database",
                    metrics: ["SOC 2 Type II", "GDPR Ready", "HIPAA Compliant"],
                  },
                ].map((feature, index) => (
                  <div
                    key={index}
                    className="group relative p-8 rounded-2xl bg-gray-900/30 backdrop-blur-sm border border-gray-800 hover:border-gray-600 transition-all duration-700 hover:scale-105 hover:-translate-y-2 hover:shadow-2xl overflow-hidden"
                  >
                    {/* Enhanced animated backgrounds */}
                    <div className="absolute inset-0 opacity-5 group-hover:opacity-20 transition-opacity duration-1000">
                      {feature.animation === "shield" && (
                        <div className="absolute inset-0">
                          {/* Rotating shield layers */}
                          <div
                            className="absolute top-1/2 left-1/2 w-32 h-32 border-2 border-red-400 rounded-full transform -translate-x-1/2 -translate-y-1/2 animate-spin"
                            style={{ animationDuration: "8s" }}
                          />
                          <div
                            className="absolute top-1/2 left-1/2 w-24 h-24 border border-red-300 rounded-full transform -translate-x-1/2 -translate-y-1/2 animate-spin"
                            style={{ animationDuration: "6s", animationDirection: "reverse" }}
                          />
                          <div className="absolute top-1/2 left-1/2 w-16 h-16 bg-red-500/20 rounded-full transform -translate-x-1/2 -translate-y-1/2 animate-pulse" />

                          {/* Security particles */}
                          <div className="absolute top-4 right-4 w-2 h-2 bg-red-400 rounded-full animate-ping" />
                          <div
                            className="absolute bottom-4 left-4 w-2 h-2 bg-red-300 rounded-full animate-ping"
                            style={{ animationDelay: "0.5s" }}
                          />
                          <div
                            className="absolute top-1/2 right-8 w-1 h-1 bg-red-500 rounded-full animate-ping"
                            style={{ animationDelay: "1s" }}
                          />
                        </div>
                      )}

                      {feature.animation === "users" && (
                        <div className="absolute inset-0">
                          {/* User permission matrix */}
                          <div className="absolute top-8 right-8 space-y-2">
                            <div className="flex space-x-1">
                              <div className="w-3 h-2 bg-blue-400 rounded animate-pulse" />
                              <div
                                className="w-2 h-2 bg-blue-300 rounded animate-pulse"
                                style={{ animationDelay: "0.2s" }}
                              />
                              <div
                                className="w-4 h-2 bg-blue-500 rounded animate-pulse"
                                style={{ animationDelay: "0.4s" }}
                              />
                            </div>
                            <div className="flex space-x-1">
                              <div
                                className="w-2 h-2 bg-blue-300 rounded animate-pulse"
                                style={{ animationDelay: "0.6s" }}
                              />
                              <div
                                className="w-3 h-2 bg-blue-400 rounded animate-pulse"
                                style={{ animationDelay: "0.8s" }}
                              />
                              <div
                                className="w-2 h-2 bg-blue-500 rounded animate-pulse"
                                style={{ animationDelay: "1s" }}
                              />
                            </div>
                          </div>

                          {/* Access flow lines */}
                          <div className="absolute bottom-8 left-8 right-8">
                            <div className="h-px bg-gradient-to-r from-blue-400 via-blue-300 to-transparent animate-pulse" />
                            <div
                              className="h-px bg-gradient-to-r from-transparent via-blue-400 to-blue-300 animate-pulse mt-2"
                              style={{ animationDelay: "0.5s" }}
                            />
                          </div>
                        </div>
                      )}

                      {feature.animation === "database" && (
                        <div className="absolute inset-0">
                          {/* Data layers */}
                          <div className="absolute top-6 right-6 space-y-1">
                            <div className="w-12 h-2 bg-green-400 rounded animate-pulse" />
                            <div
                              className="w-10 h-2 bg-green-300 rounded animate-pulse"
                              style={{ animationDelay: "0.3s" }}
                            />
                            <div
                              className="w-14 h-2 bg-green-500 rounded animate-pulse"
                              style={{ animationDelay: "0.6s" }}
                            />
                            <div
                              className="w-8 h-2 bg-green-400 rounded animate-pulse"
                              style={{ animationDelay: "0.9s" }}
                            />
                          </div>

                          {/* Compliance checkmarks */}
                          <div className="absolute bottom-8 left-8 space-x-2 flex">
                            <div className="w-3 h-3 border-2 border-green-400 rounded-full flex items-center justify-center animate-pulse">
                              <div className="w-1 h-1 bg-green-400 rounded-full" />
                            </div>
                            <div
                              className="w-3 h-3 border-2 border-green-300 rounded-full flex items-center justify-center animate-pulse"
                              style={{ animationDelay: "0.5s" }}
                            >
                              <div className="w-1 h-1 bg-green-300 rounded-full" />
                            </div>
                            <div
                              className="w-3 h-3 border-2 border-green-500 rounded-full flex items-center justify-center animate-pulse"
                              style={{ animationDelay: "1s" }}
                            >
                              <div className="w-1 h-1 bg-green-500 rounded-full" />
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="relative z-10">
                      <div
                        className={`w-18 h-18 bg-gradient-to-br ${feature.color} rounded-xl flex items-center justify-center mb-6 group-hover:scale-125 group-hover:rotate-6 transition-all duration-500 shadow-lg group-hover:shadow-xl`}
                      >
                        <feature.icon className="w-9 h-9 text-white group-hover:animate-pulse" />
                      </div>
                      <h4 className="text-xl font-bold text-white mb-4 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-white group-hover:to-gray-300 group-hover:bg-clip-text transition-all duration-300">
                        {feature.title}
                      </h4>
                      <p className="text-gray-300 text-sm leading-relaxed mb-4 group-hover:text-gray-200 transition-colors duration-300">
                        {feature.desc}
                      </p>

                      {/* Security metrics */}
                      <div className="flex flex-wrap gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-500 delay-200">
                        {feature.metrics.map((metric, i) => (
                          <span
                            key={i}
                            className="text-xs bg-white/10 px-2 py-1 rounded-full text-white border border-white/20"
                          >
                            {metric}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Deployment & Infrastructure */}
            <div className="mb-20">
              <h3 className="text-3xl font-bold text-white mb-12 text-center">Deployment & Infrastructure</h3>
              <div className="grid md:grid-cols-2 gap-8">
                <div className="group relative p-8 rounded-2xl bg-gray-900/30 backdrop-blur-sm border border-gray-800 hover:border-yellow-500/50 transition-all duration-700 overflow-hidden hover:scale-105 hover:-translate-y-2 hover:shadow-2xl hover:shadow-yellow-500/20">
                  <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700" />

                  {/* Enhanced Serverless Animation */}
                  <div className="absolute inset-0 opacity-10 group-hover:opacity-40 transition-all duration-1000">
                    {/* Scaling visualization */}
                    <div className="absolute top-8 right-8">
                      <div className="relative">
                        <div className="w-6 h-6 bg-yellow-400 rounded transform group-hover:scale-200 transition-transform duration-1000" />
                        <div className="absolute -top-4 -left-4 w-14 h-14 border-2 border-yellow-300 rounded animate-ping" />
                        <div
                          className="absolute -top-6 -left-6 w-18 h-18 border border-yellow-200 rounded animate-ping"
                          style={{ animationDelay: "0.5s" }}
                        />
                        <div
                          className="absolute -top-8 -left-8 w-22 h-22 border border-yellow-100 rounded animate-ping"
                          style={{ animationDelay: "1s" }}
                        />
                      </div>
                    </div>

                    {/* Server instances */}
                    <div className="absolute bottom-8 left-8 flex space-x-2">
                      <div className="w-2 h-8 bg-yellow-400 rounded animate-pulse" />
                      <div className="w-2 h-6 bg-yellow-300 rounded animate-pulse" style={{ animationDelay: "0.2s" }} />
                      <div
                        className="w-2 h-10 bg-yellow-500 rounded animate-pulse"
                        style={{ animationDelay: "0.4s" }}
                      />
                      <div className="w-2 h-4 bg-yellow-400 rounded animate-pulse" style={{ animationDelay: "0.6s" }} />
                      <div
                        className="w-2 h-12 bg-yellow-300 rounded animate-pulse"
                        style={{ animationDelay: "0.8s" }}
                      />
                    </div>

                    {/* Cost savings indicator */}
                    <div className="absolute top-1/2 left-8 transform -translate-y-1/2">
                      <div className="text-2xl animate-bounce">💰</div>
                      <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-yellow-400 rounded group-hover:w-16 transition-all duration-1000" />
                    </div>
                  </div>

                  <div className="relative z-10">
                    <div className="w-18 h-18 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-125 group-hover:rotate-6 transition-all duration-500 shadow-lg group-hover:shadow-yellow-500/50">
                      <Zap className="w-9 h-9 text-white group-hover:animate-bounce" />
                    </div>
                    <h4 className="text-xl font-bold text-white mb-4 group-hover:text-yellow-300 transition-colors duration-300">
                      Serverless Auto-Scaling
                    </h4>
                    <p className="text-gray-300 text-sm leading-relaxed mb-4 group-hover:text-gray-200 transition-colors duration-300">
                      Zero-idle costs via Lambda and spot A10G GPUs, handling 3,000+ users at under $300/month with
                      automatic scaling and intelligent resource management.
                    </p>

                    {/* Enhanced badges */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      <Badge
                        variant="secondary"
                        className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20 text-xs group-hover:bg-yellow-500/20 transition-colors duration-300"
                      >
                        AWS Lambda
                      </Badge>
                      <Badge
                        variant="secondary"
                        className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20 text-xs group-hover:bg-yellow-500/20 transition-colors duration-300"
                      >
                        Spot GPUs
                      </Badge>
                      <Badge
                        variant="secondary"
                        className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20 text-xs group-hover:bg-yellow-500/20 transition-colors duration-300"
                      >
                        KEDA
                      </Badge>
                    </div>

                    {/* Performance metrics */}
                    <div className="space-y-2 opacity-0 group-hover:opacity-100 transition-opacity duration-500 delay-200">
                      <div className="flex justify-between text-xs">
                        <span className="text-gray-400">Scale Time</span>
                        <span className="text-yellow-400">&lt; 30s</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span className="text-gray-400">Cost Savings</span>
                        <span className="text-yellow-400">90%</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="group relative p-8 rounded-2xl bg-gray-900/30 backdrop-blur-sm border border-gray-800 hover:border-teal-500/50 transition-all duration-700 overflow-hidden hover:scale-105 hover:-translate-y-2 hover:shadow-2xl hover:shadow-teal-500/20">
                  <div className="absolute inset-0 bg-gradient-to-br from-teal-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700" />

                  {/* Enhanced Cost Optimization Animation */}
                  <div className="absolute inset-0 opacity-10 group-hover:opacity-40 transition-all duration-1000">
                    {/* Cost reduction visualization */}
                    <div className="absolute top-8 right-8">
                      <div className="relative">
                        <div className="text-3xl group-hover:animate-bounce">💰</div>
                        <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 w-12 h-2 bg-teal-400 rounded group-hover:w-20 transition-all duration-1000" />
                        <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-teal-300 rounded group-hover:w-16 transition-all duration-1200" />
                      </div>
                    </div>

                    {/* Optimization graph */}
                    <div className="absolute bottom-8 left-8 right-16">
                      <div className="flex items-end space-x-1 h-12">
                        <div className="w-2 bg-teal-400 rounded-t animate-pulse" style={{ height: "60%" }} />
                        <div
                          className="w-2 bg-teal-300 rounded-t animate-pulse"
                          style={{ height: "40%", animationDelay: "0.1s" }}
                        />
                        <div
                          className="w-2 bg-teal-500 rounded-t animate-pulse"
                          style={{ height: "80%", animationDelay: "0.2s" }}
                        />
                        <div
                          className="w-2 bg-teal-400 rounded-t animate-pulse"
                          style={{ height: "30%", animationDelay: "0.3s" }}
                        />
                        <div
                          className="w-2 bg-teal-300 rounded-t animate-pulse"
                          style={{ height: "50%", animationDelay: "0.4s" }}
                        />
                        <div
                          className="w-2 bg-teal-500 rounded-t animate-pulse"
                          style={{ height: "20%", animationDelay: "0.5s" }}
                        />
                      </div>
                    </div>

                    {/* Efficiency particles */}
                    <div className="absolute top-1/2 left-8 transform -translate-y-1/2">
                      <div className="w-1 h-1 bg-teal-400 rounded-full animate-ping" />
                      <div
                        className="absolute top-2 left-3 w-1 h-1 bg-teal-300 rounded-full animate-ping"
                        style={{ animationDelay: "0.3s" }}
                      />
                      <div
                        className="absolute -top-1 left-6 w-1 h-1 bg-teal-500 rounded-full animate-ping"
                        style={{ animationDelay: "0.6s" }}
                      />
                    </div>
                  </div>

                  <div className="relative z-10">
                    <div className="w-18 h-18 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-125 group-hover:rotate-6 transition-all duration-500 shadow-lg group-hover:shadow-teal-500/50">
                      <Database className="w-9 h-9 text-white group-hover:animate-pulse" />
                    </div>
                    <h4 className="text-xl font-bold text-white mb-4 group-hover:text-teal-300 transition-colors duration-300">
                      Cost Optimization
                    </h4>
                    <p className="text-gray-300 text-sm leading-relaxed mb-4 group-hover:text-gray-200 transition-colors duration-300">
                      Batch processing for 50% API discounts, prompt caching reducing costs by 90%, and intelligent
                      model downgrading with real-time cost monitoring.
                    </p>

                    {/* Enhanced savings badges */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      <Badge
                        variant="secondary"
                        className="bg-teal-500/10 text-teal-400 border-teal-500/20 text-xs group-hover:bg-teal-500/20 transition-colors duration-300"
                      >
                        90% Cache Savings
                      </Badge>
                      <Badge
                        variant="secondary"
                        className="bg-teal-500/10 text-teal-400 border-teal-500/20 text-xs group-hover:bg-teal-500/20 transition-colors duration-300"
                      >
                        50% Batch Discount
                      </Badge>
                    </div>

                    {/* Cost breakdown */}
                    <div className="space-y-2 opacity-0 group-hover:opacity-100 transition-opacity duration-500 delay-200">
                      <div className="flex justify-between text-xs">
                        <span className="text-gray-400">API Costs</span>
                        <span className="text-teal-400">-90%</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span className="text-gray-400">Infrastructure</span>
                        <span className="text-teal-400">-85%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Analytics & Integration */}
            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-6">
                <h3 className="text-2xl font-bold text-white">Analytics & Reporting</h3>
                <div className="space-y-4">
                  {[
                    { text: "Usage Analytics with token consumption tracking", delay: "0ms" },
                    { text: "Performance Reports with latency optimization", delay: "200ms" },
                    { text: "Custom Dashboards with configurable metrics", delay: "400ms" },
                    { text: "ROI Calculator for cost savings estimation", delay: "600ms" },
                  ].map((feature, index) => (
                    <div
                      key={index}
                      className="group flex items-center gap-3 p-4 rounded-lg bg-gray-900/20 border border-gray-800 hover:border-gray-700 hover:bg-gray-900/40 transition-all duration-500 hover:scale-105 hover:-translate-y-1"
                    >
                      <div
                        className="w-3 h-3 bg-red-400 rounded-full animate-pulse relative group-hover:scale-150 transition-transform duration-300"
                        style={{ animationDelay: feature.delay }}
                      >
                        <div className="absolute inset-0 bg-red-400 rounded-full animate-ping opacity-0 group-hover:opacity-75" />
                      </div>
                      <span className="text-gray-300 text-sm group-hover:text-white transition-colors duration-300">
                        {feature.text}
                      </span>
                      <div className="ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <ArrowRight className="w-4 h-4 text-red-400" />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-6">
                <h3 className="text-2xl font-bold text-white">Integration & Extensibility</h3>
                <div className="space-y-4">
                  {[
                    { text: "RESTful API endpoints for custom integrations", delay: "0ms" },
                    { text: "Webhooks for real-time event notifications", delay: "200ms" },
                    { text: "Plugin System with custom processors", delay: "400ms" },
                    { text: "SSO Integration (Google, Microsoft, SAML)", delay: "600ms" },
                  ].map((feature, index) => (
                    <div
                      key={index}
                      className="group flex items-center gap-3 p-4 rounded-lg bg-gray-900/20 border border-gray-800 hover:border-gray-700 hover:bg-gray-900/40 transition-all duration-500 hover:scale-105 hover:-translate-y-1"
                    >
                      <div
                        className="w-3 h-3 bg-blue-400 rounded-full animate-pulse relative group-hover:scale-150 transition-transform duration-300"
                        style={{ animationDelay: feature.delay }}
                      >
                        <div className="absolute inset-0 bg-blue-400 rounded-full animate-ping opacity-0 group-hover:opacity-75" />
                      </div>
                      <span className="text-gray-300 text-sm group-hover:text-white transition-colors duration-300">
                        {feature.text}
                      </span>
                      <div className="ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <ArrowRight className="w-4 h-4 text-blue-400" />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Feature Stats */}
            <div className="mt-20 text-center">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                {[
                  { value: "80+", label: "Total Features", color: "text-red-400", bgColor: "bg-red-400/10" },
                  { value: "20+", label: "File Formats", color: "text-blue-400", bgColor: "bg-blue-400/10" },
                  { value: "99.9%", label: "Uptime SLA", color: "text-green-400", bgColor: "bg-green-400/10" },
                  { value: "$0", label: "Idle Costs", color: "text-yellow-400", bgColor: "bg-yellow-400/10" },
                ].map((stat, index) => (
                  <div
                    key={index}
                    className="group text-center p-6 rounded-xl bg-gray-900/20 border border-gray-800 hover:border-gray-700 transition-all duration-500 hover:scale-110 hover:-translate-y-2"
                  >
                    <div
                      className={`w-16 h-16 ${stat.bgColor} rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-125 transition-transform duration-300`}
                    >
                      <div className={`text-2xl font-bold ${stat.color}`}>{stat.value}</div>
                    </div>
                    <div className="text-gray-400 text-sm uppercase tracking-wide group-hover:text-white transition-colors duration-300">
                      {stat.label}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="py-32 px-6 bg-gradient-to-b from-gray-900/50 to-black">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-20">
              <Badge variant="secondary" className="mb-8 bg-red-500/10 text-red-400 border-red-500/20 backdrop-blur-sm">
                <Zap className="w-4 h-4 mr-2" />
                Transparent Pricing
              </Badge>
              <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
                Choose Your AI Context Engineering Plan
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-12">
                From free BYOK access to enterprise-grade managed AI credits. Scale your context engineering with
                transparent pricing and no hidden fees.
              </p>

              {/* Billing Toggle */}
              <div className="flex items-center justify-center gap-4 mb-16">
                <span className={`text-lg ${!isAnnual ? "text-white font-semibold" : "text-gray-400"}`}>Monthly</span>
                <div className="relative">
                  <input
                    type="checkbox"
                    id="billing-toggle"
                    checked={isAnnual}
                    onChange={(e) => setIsAnnual(e.target.checked)}
                    className="sr-only"
                  />
                  <label
                    htmlFor="billing-toggle"
                    className={`flex items-center cursor-pointer w-14 h-7 rounded-full p-1 transition-colors duration-300 ${
                      isAnnual ? "bg-red-600" : "bg-gray-600"
                    }`}
                  >
                    <div
                      className={`w-5 h-5 bg-white rounded-full shadow-md transform transition-transform duration-300 ${
                        isAnnual ? "translate-x-7" : "translate-x-0"
                      }`}
                    />
                  </label>
                </div>
                <span className={`text-lg ${isAnnual ? "text-white font-semibold" : "text-gray-400"}`}>
                  Annual
                  <Badge variant="secondary" className="ml-2 bg-green-500/10 text-green-400 border-green-500/20">
                    Save up to 25%
                  </Badge>
                </span>
              </div>
            </div>

            {/* Pricing Cards */}
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
              {/* Free Plan */}
              <div className="relative group p-8 rounded-2xl bg-gray-900/30 backdrop-blur-sm border border-gray-800 hover:border-gray-700 transition-all duration-300">
                <div className="text-center mb-6">
                  <Brain className="w-8 h-8 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-white mb-2">Free</h3>
                  <p className="text-gray-400 text-sm mb-4">Get Started with BYOK</p>
                  <div className="mb-4">
                    <span className="text-4xl font-bold text-white">$0</span>
                    <span className="text-gray-400 ml-1">/month</span>
                  </div>
                </div>

                <div className="space-y-4 mb-8">
                  <div className="space-y-3">
                    <h4 className="font-semibold text-white text-sm">Usage Limits</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Daily Chats</span>
                        <span className="text-white font-medium">25</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">File Uploads</span>
                        <span className="text-white font-medium">10/day (5MB each)</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Storage</span>
                        <span className="text-white font-medium">Session only</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">AI Tokens</span>
                        <span className="text-white font-medium">BYOK Required</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-semibold text-white text-sm">Core Features</h4>
                    <div className="space-y-2">
                      {["Web UI access", "Local vector search", "Community support", "Basic context engineering"].map(
                        (feature) => (
                          <div key={feature} className="flex items-center gap-2">
                            <Check className="w-4 h-4 text-green-400 flex-shrink-0" />
                            <span className="text-sm text-white">{feature}</span>
                          </div>
                        ),
                      )}
                      <div className="flex items-center gap-2">
                        <X className="w-4 h-4 text-gray-500 flex-shrink-0" />
                        <span className="text-sm text-gray-500">ConTXT-managed API credits</span>
                      </div>
                    </div>
                  </div>
                </div>

                <Button className="w-full bg-gray-800 hover:bg-gray-700 text-white border border-gray-700">
                  Get Started Free
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
                <p className="text-xs text-gray-500 text-center mt-3">Requires your own OpenAI/Anthropic API keys</p>
              </div>

              {/* Lite Plan */}
              <div className="relative group p-8 rounded-2xl bg-gray-900/30 backdrop-blur-sm border border-gray-800 hover:border-blue-500/50 transition-all duration-300">
                <div className="text-center mb-6">
                  <Zap className="w-8 h-8 text-blue-400 mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-white mb-2">Lite</h3>
                  <p className="text-gray-400 text-sm mb-4">Premium Features, Your API Keys</p>
                  <div className="mb-4">
                    <span className="text-4xl font-bold text-white">${isAnnual ? "6" : "8"}</span>
                    <span className="text-gray-400 ml-1">/month</span>
                    {isAnnual && (
                      <div className="mt-2">
                        <span className="text-sm text-gray-500 line-through">$8/month</span>
                        <Badge variant="secondary" className="ml-2 bg-green-500/10 text-green-400 text-xs">
                          Save 25%
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-4 mb-8">
                  <div className="space-y-3">
                    <h4 className="font-semibold text-white text-sm">Usage Limits</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Daily Chats</span>
                        <span className="text-white font-medium">100</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">File Uploads</span>
                        <span className="text-white font-medium">50/day (20MB each)</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Storage</span>
                        <span className="text-white font-medium">1GB</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">History</span>
                        <span className="text-white font-medium">7-day retention</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-semibold text-white text-sm">Key Features</h4>
                    <div className="space-y-2">
                      {[
                        "All Free features",
                        "Context blocks & templates",
                        "Email support (48h)",
                        "Basic analytics dashboard",
                      ].map((feature) => (
                        <div key={feature} className="flex items-center gap-2">
                          <Check className="w-4 h-4 text-green-400 flex-shrink-0" />
                          <span className="text-sm text-white">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                  Start Lite Plan
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
                <p className="text-xs text-gray-500 text-center mt-3">
                  User pays API costs + ${isAnnual ? "6" : "8"} platform fee
                </p>
              </div>

              {/* Pro Plan - Most Popular */}
              <div className="relative group p-8 rounded-2xl bg-gray-900/30 backdrop-blur-sm border border-red-500/50 shadow-2xl shadow-red-500/20 ring-1 ring-red-500/20 transition-all duration-300 hover:scale-105">
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-red-600 text-white px-4 py-1">
                    <Star className="w-3 h-3 mr-1" />
                    Most Popular
                  </Badge>
                </div>

                <div className="text-center mb-6">
                  <Shield className="w-8 h-8 text-red-400 mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-white mb-2">Pro</h3>
                  <p className="text-gray-400 text-sm mb-4">Full-Service AI Context Engineering</p>
                  <div className="mb-4">
                    <span className="text-4xl font-bold text-white">${isAnnual ? "17" : "22"}</span>
                    <span className="text-gray-400 ml-1">/month</span>
                    {isAnnual && (
                      <div className="mt-2">
                        <span className="text-sm text-gray-500 line-through">$22/month</span>
                        <Badge variant="secondary" className="ml-2 bg-green-500/10 text-green-400 text-xs">
                          Save 23%
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-4 mb-8">
                  <div className="space-y-3">
                    <h4 className="font-semibold text-white text-sm">Usage Limits</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Daily Chats</span>
                        <span className="text-white font-medium">300</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Deep Research</span>
                        <span className="text-white font-medium">40/day</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Token Budget</span>
                        <span className="text-white font-medium">1.3M/day</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Storage</span>
                        <span className="text-white font-medium">5GB</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-semibold text-white text-sm">AI Credits Included</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Monthly Tokens</span>
                        <span className="text-white font-medium">~45M mixed</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Models</span>
                        <span className="text-white font-medium">Claude, GPT-4</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Overage</span>
                        <span className="text-white font-medium">$0.02/1K tokens</span>
                      </div>
                    </div>
                  </div>
                </div>

                <Button className="w-full bg-red-600 hover:bg-red-700 text-white">
                  Start Pro Plan
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
                <p className="text-xs text-gray-500 text-center mt-3">
                  ConTXT-managed credits included (BYOK optional)
                </p>
              </div>

              {/* Team Plan */}
              <div className="relative group p-8 rounded-2xl bg-gray-900/30 backdrop-blur-sm border border-gray-800 hover:border-purple-500/50 transition-all duration-300">
                <div className="text-center mb-6">
                  <Users className="w-8 h-8 text-purple-400 mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-white mb-2">Team</h3>
                  <p className="text-gray-400 text-sm mb-4">Collaborative AI for Teams</p>
                  <div className="mb-4">
                    <span className="text-4xl font-bold text-white">${isAnnual ? "32" : "40"}</span>
                    <span className="text-gray-400 ml-1">/month</span>
                    {isAnnual && (
                      <div className="mt-2">
                        <span className="text-sm text-gray-500 line-through">$40/month</span>
                        <Badge variant="secondary" className="ml-2 bg-green-500/10 text-green-400 text-xs">
                          Save 20%
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-4 mb-8">
                  <div className="space-y-3">
                    <h4 className="font-semibold text-white text-sm">Usage Limits (Per Seat)</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Daily Chats</span>
                        <span className="text-white font-medium">900 (3× Pro)</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Deep Research</span>
                        <span className="text-white font-medium">120/day</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Token Budget</span>
                        <span className="text-white font-medium">3.9M/day</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Storage</span>
                        <span className="text-white font-medium">20GB per user</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-semibold text-white text-sm">Team Features</h4>
                    <div className="space-y-2">
                      {["Shared workspaces", "SSO integration", "SOC 2 compliance", "Dedicated support"].map(
                        (feature) => (
                          <div key={feature} className="flex items-center gap-2">
                            <Check className="w-4 h-4 text-green-400 flex-shrink-0" />
                            <span className="text-sm text-white">{feature}</span>
                          </div>
                        ),
                      )}
                    </div>
                  </div>
                </div>

                <Button className="w-full bg-purple-600 hover:bg-purple-700 text-white">
                  Start Team Plan
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
                <p className="text-xs text-gray-500 text-center mt-3">Mixed managed + BYOK options</p>
              </div>
            </div>

            {/* Feature Comparison Table */}
            <div className="bg-gray-900/30 rounded-2xl border border-gray-800 overflow-hidden">
              <div className="p-6 border-b border-gray-800">
                <h3 className="text-2xl font-bold text-white text-center">Complete Feature Comparison</h3>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-800">
                      <th className="text-left p-4 text-white font-semibold">Features</th>
                      <th className="text-center p-4 text-white font-semibold min-w-[100px]">Free</th>
                      <th className="text-center p-4 text-white font-semibold min-w-[100px]">Lite</th>
                      <th className="text-center p-4 text-white font-semibold min-w-[100px]">Pro</th>
                      <th className="text-center p-4 text-white font-semibold min-w-[100px]">Team</th>
                    </tr>
                  </thead>
                  <tbody>
                    {/* Core Features */}
                    <tr className="border-b border-gray-800/50">
                      <td colSpan={5} className="p-3 bg-gray-800/30">
                        <h4 className="font-semibold text-white text-sm">Core Features</h4>
                      </td>
                    </tr>
                    {[
                      { feature: "Web UI Access", free: true, lite: true, pro: true, team: true },
                      { feature: "Local Vector Search", free: true, lite: true, pro: true, team: true },
                      { feature: "Context Blocks & Templates", free: false, lite: true, pro: true, team: true },
                      { feature: "ConTXT-Managed API Credits", free: false, lite: false, pro: true, team: true },
                      { feature: "Advanced RAG Processing", free: false, lite: false, pro: true, team: true },
                      { feature: "Priority Processing Queue", free: false, lite: false, pro: true, team: true },
                    ].map((row) => (
                      <tr key={row.feature} className="border-b border-gray-800/30">
                        <td className="p-3 text-gray-300">{row.feature}</td>
                        <td className="p-3 text-center">
                          {row.free ? (
                            <Check className="w-4 h-4 text-green-400 mx-auto" />
                          ) : (
                            <X className="w-4 h-4 text-gray-500 mx-auto" />
                          )}
                        </td>
                        <td className="p-3 text-center">
                          {row.lite ? (
                            <Check className="w-4 h-4 text-green-400 mx-auto" />
                          ) : (
                            <X className="w-4 h-4 text-gray-500 mx-auto" />
                          )}
                        </td>
                        <td className="p-3 text-center">
                          {row.pro ? (
                            <Check className="w-4 h-4 text-green-400 mx-auto" />
                          ) : (
                            <X className="w-4 h-4 text-gray-500 mx-auto" />
                          )}
                        </td>
                        <td className="p-3 text-center">
                          {row.team ? (
                            <Check className="w-4 h-4 text-green-400 mx-auto" />
                          ) : (
                            <X className="w-4 h-4 text-gray-500 mx-auto" />
                          )}
                        </td>
                      </tr>
                    ))}

                    {/* Security & Compliance */}
                    <tr className="border-b border-gray-800/50">
                      <td colSpan={5} className="p-3 bg-gray-800/30">
                        <h4 className="font-semibold text-white text-sm">Security & Compliance</h4>
                      </td>
                    </tr>
                    {[
                      { feature: "TLS Encryption", free: "✓", lite: "✓", pro: "✓", team: "✓" },
                      { feature: "Data Retention", free: "Session", lite: "7 days", pro: "30 days", team: "90 days" },
                      { feature: "AES-256 Encryption", free: "✗", lite: "✓", pro: "✓", team: "✓" },
                      { feature: "Audit Logging", free: "✗", lite: "Basic", pro: "Full", team: "SIEM" },
                      { feature: "SOC 2 Reports", free: "✗", lite: "✗", pro: "✗", team: "✓" },
                      { feature: "SSO Integration", free: "✗", lite: "✗", pro: "✗", team: "✓" },
                    ].map((row) => (
                      <tr key={row.feature} className="border-b border-gray-800/30">
                        <td className="p-3 text-gray-300">{row.feature}</td>
                        <td className="p-3 text-center">
                          <span
                            className={`text-xs ${row.free === "✓" ? "text-green-400" : row.free === "✗" ? "text-gray-500" : "text-gray-400"}`}
                          >
                            {row.free}
                          </span>
                        </td>
                        <td className="p-3 text-center">
                          <span
                            className={`text-xs ${row.lite === "✓" ? "text-green-400" : row.lite === "✗" ? "text-gray-500" : "text-gray-400"}`}
                          >
                            {row.lite}
                          </span>
                        </td>
                        <td className="p-3 text-center">
                          <span
                            className={`text-xs ${row.pro === "✓" ? "text-green-400" : row.pro === "✗" ? "text-gray-500" : "text-gray-400"}`}
                          >
                            {row.pro}
                          </span>
                        </td>
                        <td className="p-3 text-center">
                          <span
                            className={`text-xs ${row.team === "✓" ? "text-green-400" : row.team === "✗" ? "text-gray-500" : "text-gray-400"}`}
                          >
                            {row.team}
                          </span>
                        </td>
                      </tr>
                    ))}

                    {/* Support */}
                    <tr className="border-b border-gray-800/50">
                      <td colSpan={5} className="p-3 bg-gray-800/30">
                        <h4 className="font-semibold text-white text-sm">Support</h4>
                      </td>
                    </tr>
                    <tr>
                      <td className="p-3 text-gray-300">Support Level</td>
                      <td className="p-3 text-center">
                        <span className="text-xs text-gray-400">Community</span>
                      </td>
                      <td className="p-3 text-center">
                        <span className="text-xs text-gray-400">Email (48h)</span>
                      </td>
                      <td className="p-3 text-center">
                        <span className="text-xs text-gray-400">Priority (24h)</span>
                      </td>
                      <td className="p-3 text-center">
                        <span className="text-xs text-gray-400">Dedicated</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-32 px-6 bg-gradient-to-t from-black to-gray-900/50">
          <div className="max-w-4xl mx-auto text-center text-white">
            <h2 className="text-5xl md:text-6xl font-bold mb-8">Ready to Transform Your Data?</h2>
            <p className="text-xl text-gray-300 mb-12 max-w-3xl mx-auto">
              Experience the same multi-dimensional intelligence in your own data that you just witnessed in our
              enhanced interactive mesh with depth, random movement, and intelligent distribution.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Button
                size="lg"
                className="bg-red-600 hover:bg-red-700 text-lg px-10 py-4 shadow-2xl shadow-red-600/25 hover:shadow-red-600/40 transition-all duration-300"
              >
                Get Started Free
                <ArrowRight className="ml-3 w-5 h-5" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="text-lg px-10 py-4 border-gray-600 text-gray-300 hover:bg-gray-800/50 backdrop-blur-sm bg-transparent"
              >
                Schedule Demo
              </Button>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="py-16 px-6 bg-black border-t border-gray-800">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="flex items-center space-x-3 mb-8 md:mb-0">
                <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg shadow-red-500/25">
                  <Brain className="w-6 h-6 text-white" />
                </div>
                <span className="text-2xl font-bold text-white">ConTXT</span>
              </div>

              <div className="text-center">
                <p className="text-gray-400">© 2024 ConTXT. All rights reserved. Built with ❤️ for the AI community.</p>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  )
}
