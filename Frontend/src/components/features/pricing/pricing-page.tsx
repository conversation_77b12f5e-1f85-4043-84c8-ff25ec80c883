"use client"

import { useState } from "react"
import { Check, X, Zap, Shield, Users, Brain, ArrowRight, Star, Info } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface PricingPlan {
  id: string
  name: string
  tagline: string
  monthlyPrice: number
  annualPrice: number
  popular?: boolean
  features: {
    aiCreditsIncluded: boolean
    byokRequired: boolean
    maxDailyChats: number
    maxFileSize: number
    historyRetention: number
    supportLevel: string
    teamFeatures?: boolean
  }
  quotas: {
    chatsPerDay: number
    filesPerDay: number
    storageGB: number
    tokensPerDay: number
  }
  coreFeatures: Array<{
    name: string
    included: boolean
    tooltip?: string
  }>
  security: Array<{
    feature: string
    included: boolean | string
  }>
}

const pricingPlans: PricingPlan[] = [
  {
    id: "free",
    name: "Free",
    tagline: "Get Started with BYOK",
    monthlyPrice: 0,
    annualPrice: 0,
    features: {
      aiCreditsIncluded: false,
      byokRequired: true,
      maxDailyChats: 25,
      maxFileSize: 5,
      historyRetention: 0,
      supportLevel: "community",
    },
    quotas: {
      chatsPerDay: 25,
      filesPerDay: 10,
      storageGB: 0,
      tokensPerDay: 0,
    },
    coreFeatures: [
      { name: "Web UI access", included: true },
      { name: "Local vector search", included: true },
      { name: "Community support", included: true },
      { name: "Basic context engineering", included: true },
      { name: "ConTXT-managed API credits", included: false },
      { name: "Persistent storage", included: false },
    ],
    security: [
      { feature: "TLS Encryption", included: true },
      { feature: "Data Retention", included: "Session" },
      { feature: "AES-256 Encryption", included: false },
      { feature: "Audit Logging", included: false },
    ],
  },
  {
    id: "lite",
    name: "Lite",
    tagline: "Premium Features, Your API Keys",
    monthlyPrice: 8,
    annualPrice: 6,
    features: {
      aiCreditsIncluded: false,
      byokRequired: true,
      maxDailyChats: 100,
      maxFileSize: 20,
      historyRetention: 7,
      supportLevel: "email",
    },
    quotas: {
      chatsPerDay: 100,
      filesPerDay: 50,
      storageGB: 1,
      tokensPerDay: 0,
    },
    coreFeatures: [
      { name: "All Free features", included: true },
      { name: "Context blocks and templates", included: true },
      { name: "7-day conversation history", included: true },
      { name: "Email support (48h response)", included: true },
      { name: "Enhanced rate limits", included: true },
      { name: "Basic analytics dashboard", included: true },
    ],
    security: [
      { feature: "TLS Encryption", included: true },
      { feature: "Data Retention", included: "7 days" },
      { feature: "AES-256 Encryption", included: true },
      { feature: "Audit Logging", included: "Basic" },
    ],
  },
  {
    id: "pro",
    name: "Pro",
    tagline: "Full-Service AI Context Engineering",
    monthlyPrice: 22,
    annualPrice: 17,
    popular: true,
    features: {
      aiCreditsIncluded: true,
      byokRequired: false,
      maxDailyChats: 300,
      maxFileSize: 100,
      historyRetention: 30,
      supportLevel: "priority",
    },
    quotas: {
      chatsPerDay: 300,
      filesPerDay: 100,
      storageGB: 5,
      tokensPerDay: 1300000,
    },
    coreFeatures: [
      { name: "All Lite features", included: true },
      { name: "ConTXT-managed API credits", included: true },
      { name: "Claude Sonnet & GPT-4 Turbo access", included: true },
      { name: "Advanced RAG on 5GB documents", included: true },
      { name: "30-day conversation history", included: true },
      { name: "Priority processing queue", included: true },
      { name: "Advanced analytics & insights", included: true },
      { name: "BYOK option (optional)", included: true, tooltip: "Bring Your Own Keys - optional for Pro users" },
    ],
    security: [
      { feature: "TLS Encryption", included: true },
      { feature: "Data Retention", included: "30 days" },
      { feature: "AES-256 Encryption", included: true },
      { feature: "Audit Logging", included: "Full" },
      { feature: "VPC Isolation", included: true },
    ],
  },
  {
    id: "team",
    name: "Team",
    tagline: "Collaborative AI for Teams",
    monthlyPrice: 40,
    annualPrice: 32,
    features: {
      aiCreditsIncluded: true,
      byokRequired: false,
      maxDailyChats: 900,
      maxFileSize: 100,
      historyRetention: 90,
      supportLevel: "dedicated",
      teamFeatures: true,
    },
    quotas: {
      chatsPerDay: 900,
      filesPerDay: 300,
      storageGB: 20,
      tokensPerDay: 3900000,
    },
    coreFeatures: [
      { name: "All Pro features (per seat)", included: true },
      { name: "Shared team workspaces", included: true },
      { name: "SSO integration", included: true, tooltip: "Google, Microsoft, SAML" },
      { name: "Team analytics dashboard", included: true },
      { name: "SOC 2 compliance reports", included: true },
      { name: "90-day conversation history", included: true },
      { name: "Dedicated account manager", included: true },
      { name: "Custom integrations support", included: true },
    ],
    security: [
      { feature: "TLS Encryption", included: true },
      { feature: "Data Retention", included: "90 days" },
      { feature: "AES-256 Encryption", included: true },
      { feature: "Audit Logging", included: "SIEM" },
      { feature: "SOC 2 Reports", included: true },
      { feature: "SSO Integration", included: true },
      { feature: "VPC Isolation", included: true },
    ],
  },
]

const annualDiscounts = {
  lite: 25,
  pro: 23,
  team: 20,
}

export default function PricingPage() {
  const [isAnnual, setIsAnnual] = useState(false)

  const getPrice = (plan: PricingPlan) => {
    return isAnnual ? plan.annualPrice : plan.monthlyPrice
  }

  const getSavings = (planId: string) => {
    if (planId === "free") return 0
    return annualDiscounts[planId as keyof typeof annualDiscounts] || 0
  }

  const formatTokens = (tokens: number) => {
    if (tokens === 0) return "BYOK Required"
    if (tokens >= 1000000) return `${(tokens / 1000000).toFixed(1)}M tokens/day`
    if (tokens >= 1000) return `${(tokens / 1000).toFixed(0)}K tokens/day`
    return `${tokens} tokens/day`
  }

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-black text-white">
        {/* Header */}
        <div className="relative overflow-hidden bg-gradient-to-b from-gray-900 to-black py-24">
          <div className="absolute inset-0 bg-[url('/placeholder.svg?height=800&width=1200')] opacity-5" />
          <div className="relative max-w-7xl mx-auto px-6 text-center">
            <Badge variant="secondary" className="mb-6 bg-red-500/10 text-red-400 border-red-500/20">
              <Zap className="w-4 h-4 mr-2" />
              Transparent Pricing
            </Badge>
            <h1 className="text-5xl md:text-7xl font-bold mb-8 bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent">
              Choose Your AI Context Engineering Plan
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-12">
              From free BYOK access to enterprise-grade managed AI credits. Scale your context engineering with
              transparent pricing and no hidden fees.
            </p>

            {/* Billing Toggle */}
            <div className="flex items-center justify-center gap-4 mb-16">
              <span className={`text-lg ${!isAnnual ? "text-white font-semibold" : "text-gray-400"}`}>Monthly</span>
              <Switch checked={isAnnual} onCheckedChange={setIsAnnual} className="data-[state=checked]:bg-red-600" />
              <span className={`text-lg ${isAnnual ? "text-white font-semibold" : "text-gray-400"}`}>
                Annual
                <Badge variant="secondary" className="ml-2 bg-green-500/10 text-green-400 border-green-500/20">
                  Save up to 25%
                </Badge>
              </span>
            </div>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="max-w-7xl mx-auto px-6 -mt-12 relative z-10">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {pricingPlans.map((plan) => (
              <Card
                key={plan.id}
                className={`relative bg-gray-900/50 border-gray-800 backdrop-blur-sm transition-all duration-300 hover:scale-105 ${
                  plan.popular
                    ? "border-red-500/50 shadow-2xl shadow-red-500/20 ring-1 ring-red-500/20"
                    : "hover:border-gray-700"
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-red-600 text-white px-4 py-1">
                      <Star className="w-3 h-3 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center pb-4">
                  <div className="mb-4">
                    {plan.id === "free" && <Brain className="w-8 h-8 text-gray-400 mx-auto" />}
                    {plan.id === "lite" && <Zap className="w-8 h-8 text-blue-400 mx-auto" />}
                    {plan.id === "pro" && <Shield className="w-8 h-8 text-red-400 mx-auto" />}
                    {plan.id === "team" && <Users className="w-8 h-8 text-purple-400 mx-auto" />}
                  </div>
                  <CardTitle className="text-2xl font-bold text-white">{plan.name}</CardTitle>
                  <CardDescription className="text-gray-400 text-sm">{plan.tagline}</CardDescription>

                  <div className="mt-6">
                    <div className="flex items-baseline justify-center">
                      <span className="text-4xl font-bold text-white">${getPrice(plan)}</span>
                      {plan.monthlyPrice > 0 && (
                        <span className="text-gray-400 ml-1">/{isAnnual ? "month" : "month"}</span>
                      )}
                    </div>
                    {isAnnual && plan.monthlyPrice > 0 && (
                      <div className="mt-2">
                        <span className="text-sm text-gray-500 line-through">${plan.monthlyPrice}/month</span>
                        <Badge variant="secondary" className="ml-2 bg-green-500/10 text-green-400 text-xs">
                          Save {getSavings(plan.id)}%
                        </Badge>
                      </div>
                    )}
                    {isAnnual && plan.monthlyPrice > 0 && (
                      <p className="text-xs text-gray-500 mt-1">Billed annually at ${getPrice(plan) * 12}</p>
                    )}
                  </div>
                </CardHeader>

                <CardContent className="space-y-6">
                  {/* Usage Limits */}
                  <div className="space-y-3">
                    <h4 className="font-semibold text-white text-sm">Usage Limits</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Daily Chats</span>
                        <span className="text-white font-medium">{plan.quotas.chatsPerDay}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">File Uploads</span>
                        <span className="text-white font-medium">
                          {plan.quotas.filesPerDay}/day ({plan.features.maxFileSize}MB each)
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Storage</span>
                        <span className="text-white font-medium">
                          {plan.quotas.storageGB === 0 ? "Session only" : `${plan.quotas.storageGB}GB`}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">AI Tokens</span>
                        <span className="text-white font-medium">{formatTokens(plan.quotas.tokensPerDay)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Key Features */}
                  <div className="space-y-3">
                    <h4 className="font-semibold text-white text-sm">Key Features</h4>
                    <div className="space-y-2">
                      {plan.coreFeatures.slice(0, 4).map((feature, index) => (
                        <div key={index} className="flex items-center gap-2">
                          {feature.included ? (
                            <Check className="w-4 h-4 text-green-400 flex-shrink-0" />
                          ) : (
                            <X className="w-4 h-4 text-gray-500 flex-shrink-0" />
                          )}
                          <span className={`text-sm ${feature.included ? "text-white" : "text-gray-500"}`}>
                            {feature.name}
                          </span>
                          {feature.tooltip && (
                            <Tooltip>
                              <TooltipTrigger>
                                <Info className="w-3 h-3 text-gray-400" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{feature.tooltip}</p>
                              </TooltipContent>
                            </Tooltip>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* CTA Button */}
                  <Button
                    className={`w-full ${
                      plan.popular
                        ? "bg-red-600 hover:bg-red-700 text-white"
                        : "bg-gray-800 hover:bg-gray-700 text-white border border-gray-700"
                    }`}
                  >
                    {plan.id === "free" ? "Get Started Free" : `Start ${plan.name} Plan`}
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>

                  {plan.features.byokRequired && (
                    <p className="text-xs text-gray-500 text-center">Requires your own OpenAI/Anthropic API keys</p>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Feature Comparison Table */}
        <div className="max-w-7xl mx-auto px-6 py-24">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Complete Feature Comparison</h2>
            <p className="text-xl text-gray-400">Everything you need to know about each plan</p>
          </div>

          <div className="bg-gray-900/30 rounded-2xl border border-gray-800 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-800">
                    <th className="text-left p-6 text-white font-semibold">Features</th>
                    {pricingPlans.map((plan) => (
                      <th key={plan.id} className="text-center p-6 text-white font-semibold min-w-[120px]">
                        {plan.name}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {/* Core Features */}
                  <tr className="border-b border-gray-800/50">
                    <td colSpan={5} className="p-4 bg-gray-800/30">
                      <h3 className="font-semibold text-white">Core Features</h3>
                    </td>
                  </tr>
                  {[
                    "Web UI access",
                    "Local vector search",
                    "Context blocks and templates",
                    "ConTXT-managed API credits",
                    "Advanced RAG processing",
                    "Priority processing queue",
                  ].map((feature) => (
                    <tr key={feature} className="border-b border-gray-800/30">
                      <td className="p-4 text-gray-300">{feature}</td>
                      {pricingPlans.map((plan) => {
                        const hasFeature = plan.coreFeatures.some(
                          (f) => f.name.toLowerCase().includes(feature.toLowerCase()) && f.included,
                        )
                        return (
                          <td key={plan.id} className="p-4 text-center">
                            {hasFeature ? (
                              <Check className="w-5 h-5 text-green-400 mx-auto" />
                            ) : (
                              <X className="w-5 h-5 text-gray-500 mx-auto" />
                            )}
                          </td>
                        )
                      })}
                    </tr>
                  ))}

                  {/* Security Features */}
                  <tr className="border-b border-gray-800/50">
                    <td colSpan={5} className="p-4 bg-gray-800/30">
                      <h3 className="font-semibold text-white">Security & Compliance</h3>
                    </td>
                  </tr>
                  {["TLS Encryption", "AES-256 Encryption", "Audit Logging", "SOC 2 Reports", "SSO Integration"].map(
                    (feature) => (
                      <tr key={feature} className="border-b border-gray-800/30">
                        <td className="p-4 text-gray-300">{feature}</td>
                        {pricingPlans.map((plan) => {
                          const securityFeature = plan.security.find((s) => s.feature === feature)
                          return (
                            <td key={plan.id} className="p-4 text-center">
                              {securityFeature?.included === true ? (
                                <Check className="w-5 h-5 text-green-400 mx-auto" />
                              ) : securityFeature?.included === false ? (
                                <X className="w-5 h-5 text-gray-500 mx-auto" />
                              ) : (
                                <span className="text-xs text-gray-400">{securityFeature?.included}</span>
                              )}
                            </td>
                          )
                        })}
                      </tr>
                    ),
                  )}

                  {/* Support */}
                  <tr className="border-b border-gray-800/50">
                    <td colSpan={5} className="p-4 bg-gray-800/30">
                      <h3 className="font-semibold text-white">Support</h3>
                    </td>
                  </tr>
                  <tr>
                    <td className="p-4 text-gray-300">Support Level</td>
                    {pricingPlans.map((plan) => (
                      <td key={plan.id} className="p-4 text-center">
                        <span className="text-sm text-gray-300 capitalize">{plan.features.supportLevel}</span>
                      </td>
                    ))}
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="max-w-4xl mx-auto px-6 py-24">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Frequently Asked Questions</h2>
            <p className="text-xl text-gray-400">Everything you need to know about ConTXT pricing</p>
          </div>

          <div className="space-y-8">
            {[
              {
                question: "What does BYOK mean?",
                answer:
                  "BYOK stands for 'Bring Your Own Keys'. For Free and Lite plans, you need to provide your own OpenAI or Anthropic API keys. Pro and Team plans include managed AI credits, but you can optionally use your own keys for additional cost control.",
              },
              {
                question: "How do AI credits work in Pro and Team plans?",
                answer:
                  "Pro plans include ~45M tokens monthly across embedding and completion models. Team plans include 3x that amount per seat. If you exceed your quota, additional tokens are charged at $0.02 per 1K tokens.",
              },
              {
                question: "Can I upgrade or downgrade my plan anytime?",
                answer:
                  "Yes, you can change your plan at any time. Upgrades take effect immediately, while downgrades take effect at the next billing cycle. We'll prorate any charges accordingly.",
              },
              {
                question: "What happens to my data if I cancel?",
                answer:
                  "Your data is retained according to your plan's retention policy (7-90 days). After cancellation, you have 30 days to export your data before it's permanently deleted.",
              },
              {
                question: "Do you offer enterprise plans?",
                answer:
                  "Yes! For organizations needing custom features, dedicated infrastructure, or special compliance requirements, contact our sales team for enterprise pricing and features.",
              },
            ].map((faq, index) => (
              <Card key={index} className="bg-gray-900/30 border-gray-800">
                <CardHeader>
                  <CardTitle className="text-white text-lg">{faq.question}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-300">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-red-900/20 to-red-800/20 border-t border-red-500/20">
          <div className="max-w-4xl mx-auto px-6 py-24 text-center">
            <h2 className="text-4xl font-bold text-white mb-8">Ready to Transform Your AI Context Engineering?</h2>
            <p className="text-xl text-gray-300 mb-12">
              Join thousands of developers and teams using ConTXT to build smarter AI applications with intelligent
              context engineering.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-red-600 hover:bg-red-700 text-lg px-8 py-4">
                Start Free Trial
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-4 border-gray-600 text-gray-300 bg-transparent"
              >
                Contact Sales
              </Button>
            </div>
          </div>
        </div>
      </div>
    </TooltipProvider>
  )
}
