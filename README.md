# AI Context Engineering Agent

## Project Overview

This is the **AI Context Engineering Agent**—an early-stage project focused on deliberate context curation for AI-driven software development. We're building it "slow and steady," extending the AI Context Builder to ingest data sources, analyze relevance, compress and order context, and generate prompts/rules for tools like Cursor, Windsurf, Gemini-cli, and Codex. Emphasis on engineering context without overload: short-term memory for chats, long-term for graphs/vectors, tools for external info, and structured outputs for efficiency.

## Key Features (Early Development)

- Multi-source ingestion (documents, chats, videos, URLs).
- Context analysis and curation engine.
- Tailored prompts and rules for backend/frontend/testing.
- Reusable Docker services (Neo4j, Qdrant, etc.).

## Tech Stack

- Backend: FastAPI, LangGraph, LiteLLM, Celery.
- Databases: PostgreSQL, Redis, Neo4j, Qdrant , Cognee
- Frontend: Next.js, React, Tailwind CSS.

## Getting Started


## Contributing


