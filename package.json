{"name": "saas-starter-kit", "version": "1.6.0", "private": true, "prisma": {"seed": "ts-node ./prisma/seed.ts"}, "scripts": {"release": "git checkout release && git merge origin/main && release-it && git checkout main && git merge origin/release && git push origin main", "dev": "next dev --port 4002", "build": "prisma generate && prisma db push && next build", "start": "next start --port 4002", "check-types": "tsc --pretty --noEmit", "check-format": "prettier --check .", "check-lint": "eslint -c eslint.config.cjs ./", "check-unused": "npx knip", "format": "prettier --write .", "test-all": "npm run check-format && npm run check-lint && npm run check-types && npm run build", "playwright:update": "playwright install && playwright install-deps", "test:e2e": "playwright test -x", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage --env=jsdom", "build-ci": "next build", "sync-stripe": "node --env-file .env sync-stripe.js", "check-locale": "node check-locale.js", "delete-team": "node --env-file .env delete-team.js"}, "dependencies": {"@boxyhq/metrics": "0.2.10", "@boxyhq/react-ui": "3.4.1", "@boxyhq/saml-jackson": "1.49.0", "@heroicons/react": "2.2.0", "@next-auth/prisma-adapter": "1.0.7", "@prisma/client": "6.9.0", "@react-email/components": "0.0.42", "@react-email/render": "1.1.2", "@retracedhq/logs-viewer": "2.10.1", "@retracedhq/retraced": "0.7.23", "@sentry/nextjs": "9.29.0", "@tailwindcss/typography": "0.5.16", "bcryptjs": "3.0.2", "classnames": "2.5.1", "cookies-next": "6.0.0", "currency-symbol-map": "5.1.0", "formik": "2.4.6", "i18next": "25.2.1", "micromatch": "4.0.8", "mixpanel-browser": "2.65.0", "next": "15.3.3", "next-auth": "4.24.11", "next-i18next": "15.4.2", "nodemailer": "6.10.1", "react": "18.3.1", "react-daisyui": "5.0.5", "react-dom": "18.3.1", "react-email": "4.0.16", "react-google-recaptcha": "3.1.0", "react-hot-toast": "2.5.2", "react-i18next": "15.5.3", "sharp": "0.34.2", "slack-notify": "2.0.7", "stripe": "17.7.0", "svix": "1.67.0", "swr": "2.3.3", "yup": "1.6.1", "zod": "3.25.64"}, "devDependencies": {"@faker-js/faker": "9.8.0", "@playwright/test": "1.53.0", "@testing-library/jest-dom": "6.6.3", "@types/jest": "29.5.14", "@types/micromatch": "4.0.9", "@types/mixpanel-browser": "2.60.0", "@types/node": "24.0.1", "@types/nodemailer": "6.4.17", "@types/react": "18.3.13", "@typescript-eslint/eslint-plugin": "8.34.0", "@typescript-eslint/parser": "8.34.0", "autoprefixer": "10.4.21", "daisyui": "4.12.24", "eslint": "9.28.0", "eslint-config-next": "15.3.3", "eslint-config-prettier": "10.1.5", "eslint-plugin-i18next": "6.1.1", "jest": "30.0.0", "jest-environment-jsdom": "30.0.0", "postcss": "8.5.5", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "0.6.12", "prisma": "6.9.0", "release-it": "19.0.3", "tailwindcss": "3.4.17", "ts-node": "10.9.2", "typescript": "5.8.3"}, "overrides": {"typeorm": {"mssql": ">=11.0.0"}, "react-email": {"esbuild": ">=0.25.0", "next": ">=15.2.3"}, "@react-email/components": {"prismjs": ">=1.30.0"}}}