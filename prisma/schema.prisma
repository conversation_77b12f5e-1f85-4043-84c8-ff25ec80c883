generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Role {
  ADMIN
  OWNER
  MEMBER
}

model Account {
  id                String  @id @default(uuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
}

model Session {
  id           String   @id @default(uuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model User {
  id                     String    @id @default(uuid())
  name                   String
  email                  String    @unique
  emailVerified          DateTime?
  password               String?
  image                  String?
  createdAt              DateTime  @default(now())
  updatedAt              DateTime  @default(now())
  invalid_login_attempts Int       @default(0)
  lockedAt               DateTime?

  teamMembers TeamMember[]
  accounts    Account[]
  sessions    Session[]
  invitations Invitation[]
}

model Team {
  id              String       @id @default(uuid())
  name            String
  slug            String       @unique
  domain          String?      @unique
  defaultRole     Role         @default(MEMBER)
  billingId       String?
  billingProvider String?
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @default(now())
  members         TeamMember[]
  invitations     Invitation[]
  apiKeys         ApiKey[]

  @@index([billingId])
}

model TeamMember {
  id        String   @id @default(uuid())
  teamId    String
  userId    String
  role      Role     @default(MEMBER)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  team Team @relation(fields: [teamId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([teamId, userId])
  @@index([userId])
}

model Invitation {
  id             String   @id @default(uuid())
  teamId         String
  email          String?
  role           Role     @default(MEMBER)
  token          String   @unique
  expires        DateTime
  invitedBy      String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @default(now())
  sentViaEmail   Boolean  @default(true)
  allowedDomains String[] @default([])

  user User @relation(fields: [invitedBy], references: [id], onDelete: Cascade)
  team Team @relation(fields: [teamId], references: [id], onDelete: Cascade)

  @@unique([teamId, email])
  @@index([email])
}

model PasswordReset {
  id        Int      @id @default(autoincrement())
  email     String
  token     String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  expiresAt DateTime
}

model ApiKey {
  id         String    @id @default(uuid())
  name       String
  teamId     String
  hashedKey  String    @unique
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @default(now())
  expiresAt  DateTime?
  lastUsedAt DateTime?

  team Team @relation(fields: [teamId], references: [id], onDelete: Cascade)

  @@index([teamId])
}

model Subscription {
  id         String    @id
  customerId String
  priceId    String
  active     Boolean   @default(false)
  startDate  DateTime
  endDate    DateTime
  cancelAt   DateTime?
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @default(now())

  @@index([customerId])
}

model Service {
  id          String   @id @default(uuid())
  description String
  features    String[]
  image       String
  name        String
  created     DateTime
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now())
  Price       Price[]
}

model Price {
  id            String   @id @default(uuid())
  billingScheme String
  currency      String
  serviceId     String
  amount        Int?
  metadata      Json
  type          String
  created       DateTime

  service Service @relation(fields: [serviceId], references: [id], onDelete: Cascade)
}

model jackson_store {
  key        String    @id(map: "_jackson_store_key") @db.VarChar(1500)
  value      String
  iv         String?   @db.VarChar(64)
  tag        String?   @db.VarChar(64)
  createdAt  DateTime  @default(now()) @db.Timestamp(6)
  modifiedAt DateTime? @db.Timestamp(6)
  namespace  String?   @db.VarChar(256)

  jackson_index jackson_index[]

  @@index([namespace], map: "_jackson_store_namespace")
  @@ignore
}

model jackson_index {
  id       Int    @id(map: "_jackson_index_id") @default(autoincrement())
  key      String @db.VarChar(1500)
  storeKey String @db.VarChar(1500)

  store jackson_store @relation(fields: [storeKey], references: [key], onDelete: Cascade, onUpdate: NoAction)

  @@index([key], map: "_jackson_index_key")
  @@index([key, storeKey], map: "_jackson_index_key_store")
  @@ignore
}

model jackson_ttl {
  key       String @id(map: "jackson_ttl_key") @db.VarChar(1500)
  expiresAt BigInt

  @@index([expiresAt], map: "_jackson_ttl_expires_at")
  @@ignore
}
