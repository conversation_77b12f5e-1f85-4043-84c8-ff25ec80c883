# Rule Book 1: File Upload & Processing Rules
## ConTXT Multi-Agent System

### Table of Contents
1. [Overview](#overview)
2. [Tier-Based Processing Limits](#tier-based-processing-limits)
3. [File Type Handlers](#file-type-handlers)
4. [Processing Pipelines](#processing-pipelines)
5. [Error Handling](#error-handling)
6. [Security Rules](#security-rules)
7. [Performance Optimization](#performance-optimization)
8. [Libraries & Dependencies](#libraries--dependencies)
9. [MCP Integration](#mcp-integration)
10. [Monitoring & Logging](#monitoring--logging)

### Overview
This rule book governs all file upload and processing operations within the ConTXT system. It defines strict guidelines for handling different file types across subscription tiers, ensuring scalability, security, and performance.

### Tier-Based Processing Limits

#### Free Tier Rules
```

daily_upload_limit: 50MB
concurrent_processing: 2
file_limits:
code_files: 6MB \# .py, .js, .tsx, .ts
markdown: 3MB
json: 8MB
csv: 8MB
txt: 3MB
html_xml: 3MB
readable_text: 3MB
pdf: 5MB
docx: 5MB
xlsx: 2MB
ppt: 3MB
epub: 5MB
images: 2MB \# JPG/PNG
audio: false
video: false
token_limits:
code_files: 8000 \# tokens per file

```

#### Lite Tier Rules ($11.99/month)
```

daily_upload_limit: 200MB
concurrent_processing: 5
file_limits:
code_files: 12MB
markdown: 8MB
json: 20MB
csv: 20MB
txt: 8MB
html_xml: 8MB
readable_text: 8MB
pdf: 12MB
docx: 12MB
xlsx: 8MB
ppt: 10MB
epub: 12MB
images: 8MB
audio: 15MB \# MP3/WAV, 20min limit
video: false

```

#### Pro Tier Rules ($21.99/month or $17.99/year)
```

daily_upload_limit: 1GB
concurrent_processing: 10
file_limits:
code_files: 25MB
markdown: 15MB
json: 40MB
csv: 40MB
txt: 15MB
html_xml: 15MB
readable_text: 15MB
pdf: 25MB
docx: 25MB
xlsx: 20MB
ppt: 20MB
epub: 25MB
images: 15MB
audio: 35MB \# MP3/WAV, 45min limit
video: 100MB \# MP4/MKV, 30min limit

```

#### Team Tier Rules ($34.99/month or $27.99/year)
```

daily_upload_limit: 5GB
concurrent_processing: 25
file_limits:
code_files: 50MB
markdown: 30MB
json: 80MB
csv: 80MB
txt: 30MB
html_xml: 30MB
readable_text: 30MB
pdf: 50MB
docx: 50MB
xlsx: 40MB
ppt: 40MB
epub: 50MB
images: 30MB
audio: 50MB \# MP3/WAV, 90min limit
video: 200MB \# MP4/MKV, 60min limit
team_features:
multi_user_access: true
shared_projects: true
admin_controls: true

```

### File Type Handlers

#### Code File Handler
```

class CodeFileHandler:
"""Handles code files (.py, .js, .tsx, .ts)"""

    SUPPORTED_EXTENSIONS = ['.py', '.js', '.jsx', '.ts', '.tsx', '.vue', '.svelte']

    def process(self, file_path: str, tier: str) -> Dict:
        # Syntax validation
        # Dependency extraction
        # Comment parsing
        # Function/class extraction
        # Security scanning
        pass

    def extract_metadata(self, content: str) -> Dict:
        return {
            'functions': self._extract_functions(content),
            'classes': self._extract_classes(content),
            'imports': self._extract_imports(content),
            'dependencies': self._extract_dependencies(content),
            'complexity': self._calculate_complexity(content),
            'security_issues': self._scan_security(content)
        }
    ```

#### Document Handler
```

class DocumentHandler:
"""Handles PDF, DOCX, XLSX, PPT, EPUB files"""

    def process_pdf(self, file_path: str) -> Dict:
        # Text extraction with PyMuPDF
        # Image extraction
        # Table detection
        # Metadata extraction
        pass

    def process_docx(self, file_path: str) -> Dict:
        # Text content extraction
        # Style preservation
        # Table and image handling
        # Comment extraction
        pass

    def process_xlsx(self, file_path: str) -> Dict:
        # Sheet processing
        # Formula extraction
        # Chart data
        # Pivot table analysis
        pass
    ```

#### Media Handler
```

class MediaHandler:
"""Handles images, audio, and video files"""

    def process_image(self, file_path: str) -> Dict:
        # EXIF data extraction
        # OCR text extraction
        # Object detection
        # Image classification
        pass

    def process_audio(self, file_path: str, tier: str) -> Dict:
        # Transcription (Chutes for free, RunPod for paid)
        # Speaker identification
        # Sentiment analysis
        # Key phrase extraction
        pass

    def process_video(self, file_path: str, tier: str) -> Dict:
        # Frame extraction
        # Audio transcription
        # Scene detection
        # Object tracking
        pass
    ```

### Processing Pipelines

#### Upload Pipeline
```

class UploadPipeline:
"""Main upload processing pipeline"""

    def __init__(self):
        self.validators = [
            TierLimitValidator(),
            FileTypeValidator(),
            SecurityValidator(),
            ContentValidator()
        ]

        self.processors = {
            'code': CodeFileHandler(),
            'document': DocumentHandler(),
            'media': MediaHandler(),
            'data': DataHandler()
        }

    async def process_upload(self, file_data: bytes, metadata: Dict) -> Dict:
        # Step 1: Validation
        for validator in self.validators:
            if not await validator.validate(file_data, metadata):
                raise ValidationError(validator.error_message)

        # Step 2: Type detection
        file_type = self.detect_file_type(file_data, metadata)

        # Step 3: Processing
        processor = self.processors[file_type]
        result = await processor.process(file_data, metadata)

        # Step 4: Context extraction
        context = await self.extract_context(result)

        # Step 5: Storage
        await self.store_results(context, metadata)

        return result
    ```

#### Batch Processing Pipeline
```

class BatchProcessor:
"""Handles multiple file uploads with concurrency limits"""

    def __init__(self, tier: str):
        self.tier = tier
        self.semaphore = asyncio.Semaphore(
            self.get_concurrent_limit(tier)
        )

    async def process_batch(self, files: List[Dict]) -> List[Dict]:
        tasks = []
        for file_data in files:
            task = self.process_with_semaphore(file_data)
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)
        return self.handle_batch_results(results)

    async def process_with_semaphore(self, file_data: Dict):
        async with self.semaphore:
            return await self.upload_pipeline.process_upload(file_data)
    ```

### Error Handling

#### Error Categories
```

class ProcessingError(Exception):
"""Base class for processing errors"""

    ERROR_CODES = {
        'FILE_TOO_LARGE': 1001,
        'UNSUPPORTED_FORMAT': 1002,
        'CORRUPTED_FILE': 1003,
        'TIER_LIMIT_EXCEEDED': 1004,
        'SECURITY_VIOLATION': 1005,
        'PROCESSING_TIMEOUT': 1006,
        'QUOTA_EXCEEDED': 1007,
        'INVALID_CONTENT': 1008
    }
    class TierLimitError(ProcessingError):
"""Raised when tier limits are exceeded"""
pass

class SecurityError(ProcessingError):
"""Raised when security violations are detected"""
pass

```

#### Error Recovery Strategies
```

class ErrorRecoveryManager:
"""Manages error recovery and retry logic"""

    RETRY_STRATEGIES = {
       